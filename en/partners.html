<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ICT Tilburg - Partners & Useful Links</title>
    <meta name="description" content="Partners and useful links: local initiatives, digital safety, recommended software and more.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Styles and fonts (same as other pages) -->
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        /* ...[same CSS as previous pages, omitted for brevity, please copy it accordingly]... */
        .partners-list { margin: 40px auto; max-width: 900px;}
        .partner-item { background: #fff; border-radius: 9px; box-shadow: 0 2px 8px #0001; padding: 22px; margin-bottom: 18px;}
        .partner-item h3 { margin: 0 0 7px 0; color: var(--primary);}
        .partner-item p { margin: 0 0 6px 0;}
        .partner-item a { color: var(--secondary); font-weight: bold; text-decoration: underline;}
    </style>
</head>
<body>
    <!-- Header (same as forum.html, with new menu) -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn active" aria-pressed="true">EN</button>
                    <button class="lang-btn" aria-pressed="false">NL</button>
                    <button class="lang-btn" aria-pressed="false">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo"><i class="fas fa-hands-helping"></i></div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav aria-label="Main menu">
                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false"><i class="fas fa-bars"></i></button>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="about-us.html">About Us</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="forum.html">Forum</a></li>
                        <li><a href="downloads.html">Downloads</a></li>
                        <li><a href="partners.html">Partners</a></li>
                        <li><a href="news.html">News</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <main>
        <section class="section" id="partners" aria-label="Partners & Useful Links">
            <div class="container">
                <div class="section-title">
                    <h2>Partners & Useful Links</h2>
                    <p>Links to local initiatives, digital safety, and recommended software.</p>
                </div>
                <section class="onboarding-section" aria-label="Find useful links">
                    <h3>How can these links help you?</h3>
                    <ul>
                        <li>Discover local initiatives and digital help.</li>
                        <li>Find official websites for cyber security and digital safety.</li>
                        <li>Access trusted free software and learning resources.</li>
                    </ul>
                </section>
                <div class="partners-list">
                    <div class="partner-item">
                        <h3><a href="https://www.digihulploket.nl" target="_blank">Digihulploket Tilburg</a></h3>
                        <p>Digital help desk for residents of Tilburg.</p>
                    </div>
                    <div class="partner-item">
                        <h3><a href="https://veiliginternetten.nl" target="_blank">Veilig Internetten</a></h3>
                        <p>Official Dutch site for safe internet tips and information.</p>
                    </div>
                    <div class="partner-item">
                        <h3><a href="https://www.seniorweb.nl" target="_blank">SeniorWeb</a></h3>
                        <p>Learning platform for seniors, full of digital tips and courses.</p>
                    </div>
                    <div class="partner-item">
                        <h3><a href="https://www.libreoffice.org" target="_blank">LibreOffice</a></h3>
                        <p>Recommended free office software for everyone.</p>
                    </div>
                    <div class="partner-item">
                        <h3><a href="https://www.keepass.info" target="_blank">KeePass</a></h3>
                        <p>Recommended free password manager for safe password storage.</p>
                    </div>
                </div>
            </div>
        </section>
        <section class="faq-section" id="faq" aria-label="Partners FAQ">
            <h2>Partners FAQ</h2>
            <div class="faq-list">
                <details>
                    <summary>Are these partners free to use?</summary>
                    <p>Most links are to free initiatives or software. Always check the terms on their official site.</p>
                </details>
                <details>
                    <summary>Can I suggest a new link?</summary>
                    <p>Yes! Send us your suggestion via the <a href="contact.html">contact page</a>.</p>
                </details>
                <details>
                    <summary>Does ICT Tilburg guarantee these services?</summary>
                    <p>We only list trusted sites, but cannot guarantee content or support from external partners.</p>
                </details>
            </div>
        </section>
    </main>
    <!-- Footer (same as previous pages) -->
    <footer role="contentinfo">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area"><div class="logo"><i class="fas fa-hands-helping" aria-hidden="true"></i></div><div class="logo-text">ICT<span>Tilburg</span></div></div>
                    <p>Free, innovative IT support in Tilburg and surroundings.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="about-us.html">About Us</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="forum.html">Forum</a></li>
                        <li><a href="downloads.html">Downloads</a></li>
                        <li><a href="partners.html">Partners</a></li>
                        <li><a href="news.html">News</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Our Services</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="business-management.html">Business Management</a></li>
                        <li><a href="ai-automation.html">AI & Automation</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Newsletter</h3>
                    <p>Sign up for news, tips, and invitations to free workshops.</p>
                    <form>
                        <div class="form-group"><input type="email" class="form-control" placeholder="Your email" required></div>
                        <button type="submit" class="btn">Sign Up</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. All rights reserved. | Technology for everyone, community first.</p>
            </div>
        </div>
    </footer>
    <script>
        // Mobile menu & language switcher
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
                const expanded = mobileMenuBtn.getAttribute('aria-expanded') === 'true';
                mobileMenuBtn.setAttribute('aria-expanded', (!expanded).toString());
            });
        }
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                langButtons.forEach(btn => btn.setAttribute('aria-pressed', 'false'));
                button.classList.add('active');
                button.setAttribute('aria-pressed', 'true');
            });
        });
    </script>
</body>
</html>