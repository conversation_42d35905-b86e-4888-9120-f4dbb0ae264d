<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ICT Tilburg - About Us</title>
    <meta name="description" content="Learn about ICT Tilburg: our mission, vision, team, and our commitment to free IT support for the community.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Styles and fonts -->
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        html { font-size: 18px; }
        body { font-family: var(--font-main); color: var(--dark); background-color: #f0f4f8; }
        .container { width: 95%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 1rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 1rem; opacity: 0.7; }
        .lang-btn.active, .lang-btn:focus { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 2rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.15rem; transition: color 0.3s; position: relative; }
        nav a:focus { outline: 2px solid var(--secondary); outline-offset: 2px; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.7rem; cursor: pointer; color: var(--primary); }
        @media (max-width: 900px) {
            .about-content { flex-direction: column; gap: 35px; }
        }
        @media (max-width: 768px) {
            nav ul { display: none; position: absolute; background: #fff; left: 0; width: 100%; flex-direction: column; gap: 0; top: 100%; z-index: 100; }
            nav ul.show { display: flex; }
            .mobile-menu-btn { display: block; }
        }
        .section { padding: 80px 0 40px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .about-content { display: flex; align-items: flex-start; gap: 60px; }
        .about-text { flex: 2; }
        .about-team { flex: 1; }
        .team-list { margin-top: 20px; padding-left: 0; list-style: none; }
        .team-list li { display: flex; align-items: center; margin-bottom: 15px; }
        .team-photo { width: 48px; height: 48px; border-radius: 50%; background: var(--light); margin-right: 14px; object-fit: cover; border: 2px solid var(--secondary);}
        .onboarding-section, .faq-section {
            background: #eafbf3;
            border-left: 5px solid #43b380;
            padding: 18px 28px;
            margin-bottom: 38px;
            border-radius: 8px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }
        .onboarding-section h3 { margin-top: 0; color: var(--primary);}
        .onboarding-section ol { margin: 13px 0 10px 18px; }
        .onboarding-section a { color: var(--secondary); font-weight: bold; text-decoration: underline; }
        .faq-section { margin-top: 60px; }
        .faq-section h2 { color: var(--primary);}
        .faq-list details { margin-bottom: 12px; background: #fff; border-radius: 5px; padding: 7px 14px;}
        .faq-list summary { font-weight: 600; cursor: pointer;}
        .faq-list p { margin: 10px 0 5px 0; color: var(--dark);}
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a:focus { outline: 2px solid var(--secondary);}
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="E-mail ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn active" aria-pressed="true">EN</button>
                    <button class="lang-btn" aria-pressed="false">NL</button>
                    <button class="lang-btn" aria-pressed="false">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <div class="logo-area">
                    <div class="logo" aria-label="Logo">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="logo-text">ICT<span>Tilburg</span></div>
                </div>
                <nav aria-label="Main menu">
                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>
                    <ul>
                        <li><a href="index.html#home" tabindex="0">Home</a></li>
                        <li><a href="services.html" tabindex="0">Services</a></li>
                        <li><a href="about-us.html" tabindex="0">About Us</a></li>
                        <li><a href="workshops.html" tabindex="0">Workshops</a></li>
                        <li><a href="blog.html" tabindex="0">Blog</a></li>
                        <li><a href="contact.html" tabindex="0">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <!-- About Us Section -->
    <main>
        <section class="section" id="about-us" aria-label="About ICT Tilburg">
            <div class="container">
                <div class="section-title">
                    <h2>About Us</h2>
                    <p>ICT Tilburg is a community initiative offering free and innovative IT support to everyone in Tilburg and surroundings.</p>
                </div>
                <!-- Onboarding block -->
                <section class="onboarding-section" aria-label="First time?">
                  <h3>Is this your first time here? Here’s how it works:</h3>
                  <ol>
                    <li>Choose a service or workshop that suits you.</li>
                    <li>Ask your question via the <a href="contact.html">contact form</a> or call us.</li>
                    <li>We will get in touch and plan a solution together.</li>
                    <li>Enjoy free, personal IT support!</li>
                  </ol>
                  <p><b>Tip:</b> Check the <a href="#faq">FAQ</a> below for more answers!</p>
                </section>
                <div class="about-content" style="margin-bottom: 40px;">
                    <div class="about-text">
                        <h3>Mission</h3>
                        <p>
                            We believe technology should be accessible to everyone. Our mission is to break down barriers and promote digital self-reliance – for young and old, individuals or entrepreneurs.
                        </p>
                        <h3>Vision</h3>
                        <p>
                            A society where everyone uses digital opportunities safely, consciously and independently. We strive for inclusion, knowledge sharing and a strong local community.
                        </p>
                        <h3>What do we do?</h3>
                        <ul>
                            <li>Free IT support remotely and on location</li>
                            <li>Workshops, training sessions and information</li>
                            <li>Advice on open source, privacy and digital safety</li>
                            <li>Projects with social impact in collaboration with partners</li>
                        </ul>
                    </div>
                    <aside class="about-team">
                        <h3>Our Team</h3>
                        <ul class="team-list">
                            <li>
                                <img class="team-photo" src="https://randomuser.me/api/portraits/men/32.jpg" alt="João - IT Specialist">
                                <span>João – IT Specialist</span>
                            </li>
                            <li>
                                <img class="team-photo" src="https://randomuser.me/api/portraits/women/44.jpg" alt="Sanne - Community & Training">
                                <span>Sanne – Community & Training</span>
                            </li>
                            <li>
                                <img class="team-photo" src="https://randomuser.me/api/portraits/men/65.jpg" alt="Erik - Web & Automation">
                                <span>Erik – Web & Automation</span>
                            </li>
                        </ul>
                    </aside>
                </div>
            </div>
        </section>
        <!-- FAQ block -->
        <section class="faq-section" id="faq" aria-label="Frequently Asked Questions" style="margin-top:60px;">
          <h2>Frequently Asked Questions (FAQ)</h2>
          <div class="faq-list">
            <details>
              <summary>Who is ICT Tilburg for?</summary>
              <p>For everyone in Tilburg and surroundings who needs digital help: seniors, families, entrepreneurs, associations and the curious.</p>
            </details>
            <details>
              <summary>Are the services really free?</summary>
              <p>Yes, our basic services are free for individuals, non-profits and small organizations. Special projects upon request.</p>
            </details>
            <details>
              <summary>Do I need to know a lot about computers?</summary>
              <p>Not at all! We help both beginners and advanced users, step by step.</p>
            </details>
            <details>
              <summary>How quickly do I get help?</summary>
              <p>We usually respond within 24 hours to your request.</p>
            </details>
            <details>
              <summary>Can I also visit in person?</summary>
              <p>Yes, you can make an appointment for help on location in Tilburg.</p>
            </details>
          </div>
        </section>
    </main>
    <!-- Footer -->
    <footer role="contentinfo">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo" aria-label="Logo">
                            <i class="fas fa-hands-helping" aria-hidden="true"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Free, innovative IT support in Tilburg and surroundings.</p>
                    <div class="social-links" aria-label="Social media">
                        <a href="#" tabindex="0" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" tabindex="0" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" tabindex="0" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" tabindex="0" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html#home">Home</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="about-us.html">About Us</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Our Services</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="business-management.html">Business Management</a></li>
                        <li><a href="ai-automation.html">AI & Automation</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Newsletter</h3>
                    <p>Sign up for news, tips, and invitations to free workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Your email" required>
                        </div>
                        <button type="submit" class="btn">Sign Up</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 ICT Tilburg. All rights reserved. | Technology for everyone, community first.</p>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle with ARIA
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
                const expanded = mobileMenuBtn.getAttribute('aria-expanded') === 'true';
                mobileMenuBtn.setAttribute('aria-expanded', (!expanded).toString());
            });
        }
        // Language switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                langButtons.forEach(btn => btn.setAttribute('aria-pressed', 'false'));
                button.classList.add('active');
                button.setAttribute('aria-pressed', 'true');
            });
        });
    </script>
</body>
</html>