<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Over Ons - ICT Tilburg | Digitale Inclusie Door Vrijwilligers</title>
    <meta name="description" content="<PERSON><PERSON> meer over ICT Tilburg - een vrijwilligersorganisatie die zich inzet voor digitale inclusie in Tilburg. Gratis IT ondersteuning voor iedereen.">
    <meta name="keywords" content="ICT Tilburg, over ons, vrijwilligers, digitale inclusie, IT ondersteuning, Tilburg gemeenschap, non-profit">
    <meta name="author" content="ICT Tilburg">
    <meta property="og:title" content="Over Ons - ICT Tilburg">
    <meta property="og:description" content="Ontdek het verhaal achter ICT Tilburg - vrijwilligers die zich inzetten voor digitale inclusie en gratis IT ondersteuning.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://icttilburg.nl/nl/over-ons.html">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Over Ons - ICT Tilburg">
    <meta name="twitter:description" content="Het verhaal van ICT Tilburg - vrijwilligers die technologie toegankelijk maken voor iedereen in Tilburg.">
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/footer-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: var(--font-main); color: var(--dark); line-height: 1.6; background-color: #f0f4f8; }
        .container { width: 90%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
        .lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
        /* Enhanced About Us Styles */
        .section { padding: 80px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; font-size: 1.1rem; }

        /* Hero Section */
        .about-hero {
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
            color: white;
            padding: 100px 0;
        }

        .about-hero .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .about-hero .hero-subtitle {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .about-hero .hero-description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .about-hero .hero-stats {
            display: flex;
            gap: 30px;
            margin-top: 40px;
        }

        .about-hero .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            line-height: 1;
        }

        .about-hero .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .about-hero .hero-image img {
            width: 100%;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            margin-top: 30px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .btn-primary {
            background: var(--secondary);
            color: white;
            border-color: var(--secondary);
        }

        .btn-primary:hover {
            background: var(--accent);
            border-color: var(--accent);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border-color: white;
        }

        .btn-secondary:hover {
            background: white;
            color: var(--primary);
        }
        /* Mission Section */
        .our-mission {
            background: #f8f9fa;
        }

        .mission-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: start;
            margin-top: 50px;
        }

        .mission-text h3 {
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .mission-text p {
            color: var(--gray);
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .mission-highlight {
            background: linear-gradient(135deg, var(--secondary), var(--accent));
            border-radius: 15px;
            padding: 25px;
            color: white;
            margin: 30px 0;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .highlight-icon {
            font-size: 2.5rem;
            opacity: 0.9;
        }

        .highlight-content h4 {
            color: white;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .highlight-content p {
            color: white;
            opacity: 0.9;
            margin: 0;
        }

        .mission-values {
            margin-top: 40px;
        }

        .mission-values h4 {
            color: var(--dark);
            margin-bottom: 25px;
            font-size: 1.4rem;
        }

        .values-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .value-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }

        .value-item i {
            color: var(--primary);
            font-size: 1.5rem;
        }

        .value-item h5 {
            color: var(--dark);
            margin-bottom: 5px;
            font-size: 1rem;
        }

        .value-item p {
            color: var(--gray);
            font-size: 0.9rem;
            margin: 0;
            line-height: 1.4;
        }

        .mission-image {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .mission-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        /* Story Timeline */
        .our-story {
            padding: 80px 0;
        }

        .story-timeline {
            margin-top: 50px;
            position: relative;
        }

        .story-timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--secondary);
            transform: translateX(-50%);
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 50px;
            position: relative;
        }

        .timeline-item:nth-child(odd) {
            flex-direction: row;
        }

        .timeline-item:nth-child(even) {
            flex-direction: row-reverse;
        }

        .timeline-year {
            background: var(--primary);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.2rem;
            position: relative;
            z-index: 2;
        }

        .timeline-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin: 0 30px;
            flex: 1;
        }

        .timeline-content h3 {
            color: var(--primary);
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .timeline-content p {
            color: var(--gray);
            line-height: 1.6;
            margin: 0;
        }
        /* Team Section */
        .our-team {
            background: #f8f9fa;
        }

        .team-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 50px;
            color: var(--gray);
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .team-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
            transition: transform 0.3s ease;
            position: relative;
        }

        .team-card:hover {
            transform: translateY(-5px);
        }

        .team-card.featured {
            border: 2px solid var(--secondary);
        }

        .team-card.featured::before {
            content: "Oprichter";
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--secondary);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .team-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: var(--secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .team-card h5 {
            font-size: 1.3rem;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .team-role {
            color: var(--secondary);
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .team-description {
            color: var(--gray);
            line-height: 1.5;
            margin-bottom: 20px;
            font-size: 0.95rem;
        }

        .team-skills {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .skill {
            background: var(--light);
            color: var(--primary);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .volunteer-cta {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            color: white;
            margin-top: 50px;
        }

        .volunteer-cta h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.8rem;
        }

        .volunteer-cta p {
            color: white;
            opacity: 0.9;
            margin-bottom: 25px;
            font-size: 1.1rem;
        }

        .volunteer-cta .btn {
            background: white;
            color: var(--primary);
            border-color: white;
        }

        .volunteer-cta .btn:hover {
            background: var(--light);
            transform: translateY(-2px);
        }

        /* How We Work */
        .how-we-work {
            padding: 80px 0;
        }

        .work-process {
            margin-top: 50px;
        }

        .process-steps {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }

        .process-step {
            display: flex;
            align-items: center;
            gap: 30px;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .process-step:hover {
            transform: translateX(10px);
        }

        .step-icon {
            width: 80px;
            height: 80px;
            background: var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            flex-shrink: 0;
        }

        .step-content h3 {
            color: var(--primary);
            margin-bottom: 10px;
            font-size: 1.4rem;
        }

        .step-content p {
            color: var(--gray);
            line-height: 1.6;
            margin: 0;
        }
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        /* Contact Ways */
        .contact-ways {
            background: #f8f9fa;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .contact-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            text-align: center;
        }

        .contact-card:hover {
            transform: translateY(-5px);
        }

        .contact-card.primary {
            border: 2px solid var(--secondary);
        }

        .contact-icon {
            width: 70px;
            height: 70px;
            background: var(--light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: 1.8rem;
            margin: 0 auto 20px;
        }

        .contact-card.primary .contact-icon {
            background: var(--secondary);
            color: white;
        }

        .contact-content h4 {
            color: var(--primary);
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .contact-content p {
            color: var(--gray);
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .contact-info {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 10px;
        }

        .contact-link {
            color: var(--secondary);
            text-decoration: none;
            font-weight: 600;
        }

        .contact-link:hover {
            text-decoration: underline;
        }

        .contact-hours {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: var(--gray);
            font-size: 0.9rem;
        }

        .contact-hours i {
            color: var(--primary);
        }

        /* Community Impact */
        .community-impact {
            padding: 80px 0;
        }

        .impact-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .impact-stat {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .impact-stat:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 80px;
            height: 80px;
            background: var(--light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: 2rem;
            margin: 0 auto 20px;
        }

        .stat-content .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            line-height: 1;
        }

        .stat-content .stat-label {
            font-weight: 600;
            color: var(--dark);
            margin: 10px 0 5px;
        }

        .stat-content .stat-description {
            color: var(--gray);
            font-size: 0.9rem;
        }

        .impact-stories {
            margin-top: 60px;
        }

        .impact-stories h3 {
            text-align: center;
            color: var(--primary);
            margin-bottom: 40px;
            font-size: 1.8rem;
        }

        .stories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .story-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .story-card:hover {
            transform: translateY(-5px);
        }

        .story-quote {
            margin-bottom: 20px;
        }

        .story-quote i {
            color: var(--secondary);
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .story-quote p {
            color: var(--gray);
            font-style: italic;
            line-height: 1.6;
            margin: 0;
        }

        .story-author strong {
            color: var(--primary);
            display: block;
            margin-bottom: 5px;
        }

        .story-author span {
            color: var(--gray);
            font-size: 0.9rem;
        }

        /* Call to Action */
        .cta-section {
            background: var(--primary);
            color: white;
        }

        .cta-content {
            text-align: center;
        }

        .cta-text h2 {
            color: white;
            margin-bottom: 20px;
            font-size: 2.2rem;
        }

        .cta-text p {
            color: white;
            opacity: 0.9;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .cta-option {
            text-align: center;
        }

        .cta-option i {
            font-size: 3rem;
            color: var(--secondary);
            margin-bottom: 20px;
        }

        .cta-option h4 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .cta-option p {
            color: white;
            opacity: 0.8;
            margin-bottom: 25px;
        }

        .cta-option .btn {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.3);
            color: white;
        }

        .cta-option .btn-primary {
            background: var(--secondary);
            border-color: var(--secondary);
        }

        .cta-option .btn:hover {
            background: white;
            color: var(--primary);
        }

        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .about-hero .hero-content,
            .mission-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .values-grid {
                grid-template-columns: 1fr;
            }

            .story-timeline::before {
                left: 30px;
            }

            .timeline-item {
                flex-direction: column;
                align-items: flex-start;
                padding-left: 60px;
            }

            .timeline-item:nth-child(even) {
                flex-direction: column;
            }

            .timeline-year {
                position: absolute;
                left: 0;
                margin: 0;
            }

            .timeline-content {
                margin: 0;
                width: 100%;
            }

            .process-step {
                flex-direction: column;
                text-align: center;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .hero-stats {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .cta-options {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 992px) {
            .team-grid, .contact-grid, .footer-grid { grid-template-columns: 1fr; }
        }
    </style>
    <script src="../assets/js/common.js"></script>
    <link rel="alternate" hreflang="en" href="../en/about-us.html">
    <link rel="alternate" hreflang="pt" href="../pt/sobre-nos.html">
    <link rel="canonical" href="over-ons.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="Email ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn" aria-pressed="false" data-lang="en" data-href="../en/about-us.html">EN</button>
                    <button class="lang-btn active" aria-pressed="true" data-lang="nl" data-href="over-ons.html">NL</button>
                    <button class="lang-btn" aria-pressed="false" data-lang="pt" data-href="../pt/sobre-nos.html">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <nav aria-label="Main navigation">
                    <div class="logo-area">
                        <a href="index.html" class="logo-link">
                            <div class="logo" aria-label="Logo">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <div class="logo-text">ICT<span>Tilburg</span></div>
                        </a>
                    </div>

                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>

                    <ul class="nav-links">
                        <li><a href="index.html" class="nav-item">Home</a></li>
                        <li><a href="over-ons.html" class="nav-item">Over Ons</a></li>
                        <li><a href="diensten.html" class="nav-item">Diensten</a></li>
                        <li><a href="workshops.html" class="nav-item">Workshops</a></li>
                        <li><a href="blog.html" class="nav-item">Blog & Nieuws</a></li>
                        <li><a href="forum.html" class="nav-item">Forum</a></li>
                        <li><a href="downloads.html" class="nav-item">Downloads</a></li>
                        <li><a href="partners.html" class="nav-item">Partners</a></li>
                        <li><a href="contact.html" class="nav-item">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
    <!-- Hero Section -->
    <section class="hero about-hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>Over ICT Tilburg</h1>
                    <p class="hero-subtitle">Vrijwilligers die technologie toegankelijk maken voor iedereen</p>
                    <p class="hero-description">Wij zijn een gemeenschapsgedreven organisatie die zich inzet voor digitale inclusie in Tilburg. Door vrijwilligerswerk maken we professionele IT ondersteuning gratis beschikbaar voor iedereen.</p>
                    <div class="hero-buttons">
                        <a href="#our-mission" class="btn btn-primary">
                            <i class="fas fa-heart"></i> Onze Missie
                        </a>
                        <a href="#our-team" class="btn btn-secondary">
                            <i class="fas fa-users"></i> Ons Team
                        </a>
                    </div>
                    <div class="hero-stats">
                        <div class="stat">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Geholpen Mensen</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">3</div>
                            <div class="stat-label">Jaar Actief</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Gratis Service</div>
                        </div>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="ICT Tilburg vrijwilligers helpen gemeenschap" loading="lazy">
                </div>
            </div>
        </div>
    </section>

    <!-- Our Mission Section -->
    <section class="section our-mission" id="our-mission">
        <div class="container">
            <div class="section-title">
                <h2>Onze Missie</h2>
                <p>Digitale inclusie voor iedereen in Tilburg - dat is waar wij voor staan</p>
            </div>
            <div class="mission-content">
                <div class="mission-text">
                    <h3>Technologie voor iedereen toegankelijk maken</h3>
                    <p>ICT Tilburg is ontstaan vanuit de overtuiging dat <strong>niemand buitengesloten mag worden</strong> van de digitale wereld. Of je nu senior bent die wil leren videobellen, een kleine ondernemer die een website nodig heeft, of een vereniging die hulp zoekt bij administratie - wij zijn er voor je.</p>

                    <div class="mission-highlight">
                        <div class="highlight-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="highlight-content">
                            <h4>100% Gratis Vrijwilligerswerk</h4>
                            <p>Alle onze diensten zijn volledig gratis. Onze vrijwilligers zetten zich belangeloos in omdat we geloven dat digitale vaardigheden een basisrecht zijn, geen luxe.</p>
                        </div>
                    </div>

                    <div class="mission-values">
                        <h4>Onze Kernwaarden</h4>
                        <div class="values-grid">
                            <div class="value-item">
                                <i class="fas fa-users"></i>
                                <h5>Inclusiviteit</h5>
                                <p>Iedereen is welkom, ongeacht leeftijd, achtergrond of technische kennis</p>
                            </div>
                            <div class="value-item">
                                <i class="fas fa-graduation-cap"></i>
                                <h5>Educatie</h5>
                                <p>We leren mensen niet alleen oplossingen, maar ook hoe ze zelf problemen kunnen voorkomen</p>
                            </div>
                            <div class="value-item">
                                <i class="fas fa-leaf"></i>
                                <h5>Duurzaamheid</h5>
                                <p>We geven oude computers een tweede leven en promoten open source software</p>
                            </div>
                            <div class="value-item">
                                <i class="fas fa-handshake"></i>
                                <h5>Gemeenschap</h5>
                                <p>We bouwen aan een sterke, verbonden digitale gemeenschap in Tilburg</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mission-image">
                    <img src="https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="Digitale inclusie en gemeenschap" loading="lazy">
                </div>
            </div>
        </div>
    </section>

    <!-- Our Story Section -->
    <section class="section our-story">
        <div class="container">
            <div class="section-title">
                <h2>Ons Verhaal</h2>
                <p>Hoe ICT Tilburg begon en groeide tot een belangrijke pijler in de gemeenschap</p>
            </div>
            <div class="story-timeline">
                <div class="timeline-item">
                    <div class="timeline-year">2022</div>
                    <div class="timeline-content">
                        <h3>Het Begin</h3>
                        <p>ICT Tilburg werd opgericht door een groep IT professionals die zagen dat veel mensen in hun omgeving worstelden met technologie. We begonnen met het helpen van familie en vrienden.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2023</div>
                    <div class="timeline-content">
                        <h3>Eerste Workshops</h3>
                        <p>We organiseerden onze eerste workshops over digitale veiligheid in het wijkcentrum. De respons was overweldigend - binnen een maand hadden we 50 deelnemers.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2024</div>
                    <div class="timeline-content">
                        <h3>Groei en Uitbreiding</h3>
                        <p>Ons team groeide naar 8 vrijwilligers. We breidden uit met remote support, bedrijfsondersteuning en AI workshops. Meer dan 300 mensen geholpen dit jaar.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2025</div>
                    <div class="timeline-content">
                        <h3>De Toekomst</h3>
                        <p>We blijven groeien en innoveren. Nieuwe partnerships met lokale organisaties en uitbreiding van onze diensten staan op de planning.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Team Section -->
    <section class="section our-team" id="our-team">
        <div class="container">
            <div class="section-title">
                <h2>Ons Vrijwilligersteam</h2>
                <p>Ontmoet de toegewijde vrijwilligers die ICT Tilburg mogelijk maken</p>
            </div>
            <div class="team-intro">
                <p>Ons team bestaat uit ervaren IT professionals, gepensioneerde experts en enthousiaste studenten die hun kennis en tijd vrijwillig inzetten voor de gemeenschap. Samen brengen we meer dan 50 jaar ervaring mee in verschillende IT disciplines.</p>
            </div>
            <div class="team-grid">
                <div class="team-card featured">
                    <div class="team-avatar"><i class="fas fa-user-tie"></i></div>
                    <h5>Marco van der Berg</h5>
                    <p class="team-role">Oprichter & Coördinator</p>
                    <p class="team-description">15+ jaar ervaring in IT consultancy. Gespecialiseerd in Linux, open source en digitale transformatie voor MKB.</p>
                    <div class="team-skills">
                        <span class="skill">Linux</span>
                        <span class="skill">Open Source</span>
                        <span class="skill">Projectmanagement</span>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-graduate"></i></div>
                    <h5>Els Janssen</h5>
                    <p class="team-role">Senior Trainer & Workshops</p>
                    <p class="team-description">Gepensioneerd docent met passie voor het leren van digitale vaardigheden aan senioren en beginners.</p>
                    <div class="team-skills">
                        <span class="skill">Training</span>
                        <span class="skill">Workshops</span>
                        <span class="skill">Digitale Veiligheid</span>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-cog"></i></div>
                    <h5>Tom Hendriks</h5>
                    <p class="team-role">AI & Automatisering Specialist</p>
                    <p class="team-description">Software engineer die AI toegankelijk maakt voor kleine bedrijven en non-profit organisaties.</p>
                    <div class="team-skills">
                        <span class="skill">AI Development</span>
                        <span class="skill">Automatisering</span>
                        <span class="skill">Python</span>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-shield"></i></div>
                    <h5>Sarah de Vries</h5>
                    <p class="team-role">Cybersecurity & Privacy Expert</p>
                    <p class="team-description">Cybersecurity professional die zich inzet voor veilig internetten en privacy bescherming.</p>
                    <div class="team-skills">
                        <span class="skill">Cybersecurity</span>
                        <span class="skill">Privacy</span>
                        <span class="skill">Ethical Hacking</span>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-laptop"></i></div>
                    <h5>Kevin Bakker</h5>
                    <p class="team-role">Web Developer & Designer</p>
                    <p class="team-description">Freelance webontwikkelaar die gratis websites bouwt voor lokale verenigingen en goede doelen.</p>
                    <div class="team-skills">
                        <span class="skill">Web Development</span>
                        <span class="skill">UI/UX Design</span>
                        <span class="skill">WordPress</span>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar"><i class="fas fa-user-nurse"></i></div>
                    <h5>Linda Pieters</h5>
                    <p class="team-role">Support Coördinator</p>
                    <p class="team-description">Zorgt voor de coördinatie van support aanvragen en begeleidt nieuwe vrijwilligers.</p>
                    <div class="team-skills">
                        <span class="skill">Customer Support</span>
                        <span class="skill">Coördinatie</span>
                        <span class="skill">Communicatie</span>
                    </div>
                </div>
            </div>

            <div class="volunteer-cta">
                <div class="volunteer-cta-content">
                    <h3>Word ook vrijwilliger!</h3>
                    <p>Heb je IT kennis en wil je iets betekenen voor de gemeenschap? We zijn altijd op zoek naar nieuwe vrijwilligers die onze missie willen ondersteunen.</p>
                    <a href="contact.html" class="btn btn-primary">
                        <i class="fas fa-hands-helping"></i> Word Vrijwilliger
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- How We Work Section -->
    <section class="section how-we-work">
        <div class="container">
            <div class="section-title">
                <h2>Hoe Wij Werken</h2>
                <p>Ons bewezen proces zorgt voor effectieve hulp en tevreden gebruikers</p>
            </div>
            <div class="work-process">
                <div class="process-steps">
                    <div class="process-step">
                        <div class="step-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="step-content">
                            <h3>1. Contact Opnemen</h3>
                            <p>Via telefoon, WhatsApp, e-mail of ons contactformulier. We reageren altijd binnen 24 uur en luisteren goed naar uw vraag.</p>
                        </div>
                    </div>

                    <div class="process-step">
                        <div class="step-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="step-content">
                            <h3>2. Probleem Analyseren</h3>
                            <p>We stellen de juiste vragen om uw situatie te begrijpen en bepalen de beste aanpak voor uw specifieke probleem.</p>
                        </div>
                    </div>

                    <div class="process-step">
                        <div class="step-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="step-content">
                            <h3>3. Oplossing Implementeren</h3>
                            <p>We lossen het probleem op via remote support of een persoonlijke afspraak, afhankelijk van wat het beste uitkomt.</p>
                        </div>
                    </div>

                    <div class="process-step">
                        <div class="step-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="step-content">
                            <h3>4. Kennis Overdragen</h3>
                            <p>We leggen uit wat we hebben gedaan en leren u hoe u soortgelijke problemen in de toekomst kunt voorkomen.</p>
                        </div>
                    </div>

                    <div class="process-step">
                        <div class="step-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="step-content">
                            <h3>5. Doorlopende Ondersteuning</h3>
                            <p>Ook na de oplossing blijven we beschikbaar voor vragen en verdere ondersteuning. Dat is onze belofte.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Ways Section -->
    <section class="section contact-ways">
        <div class="container">
            <div class="section-title">
                <h2>Hoe Kunt U Ons Bereiken?</h2>
                <p>Meerdere manieren om contact op te nemen - kies wat het beste bij u past</p>
            </div>
            <div class="contact-grid">
                <div class="contact-card primary">
                    <div class="contact-icon"><i class="fas fa-phone"></i></div>
                    <div class="contact-content">
                        <h4>Telefoon & WhatsApp</h4>
                        <p>Direct contact voor urgente vragen of een persoonlijk gesprek</p>
                        <div class="contact-info">
                            <strong>+31 6 1234 5678</strong>
                        </div>
                        <div class="contact-hours">
                            <i class="fas fa-clock"></i> Ma-Vr: 9:00-18:00
                        </div>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-icon"><i class="fas fa-envelope"></i></div>
                    <div class="contact-content">
                        <h4>E-mail Support</h4>
                        <p>Stuur uw vraag per e-mail met eventuele screenshots of details</p>
                        <div class="contact-info">
                            <strong><EMAIL></strong>
                        </div>
                        <div class="contact-hours">
                            <i class="fas fa-reply"></i> Reactie binnen 24 uur
                        </div>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-icon"><i class="fas fa-map-marker-alt"></i></div>
                    <div class="contact-content">
                        <h4>Persoonlijke Afspraak</h4>
                        <p>Face-to-face ondersteuning in het wijkcentrum of bij u thuis</p>
                        <div class="contact-info">
                            <strong>Wijkcentrum Tilburg</strong><br>
                            Heuvelring 122, 5038 CL Tilburg
                        </div>
                        <div class="contact-hours">
                            <i class="fas fa-calendar"></i> Op afspraak
                        </div>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-icon"><i class="fas fa-comments"></i></div>
                    <div class="contact-content">
                        <h4>Online Formulier</h4>
                        <p>Gebruik ons contactformulier voor gedetailleerde vragen</p>
                        <div class="contact-info">
                            <a href="contact.html" class="contact-link">Naar contactformulier</a>
                        </div>
                        <div class="contact-hours">
                            <i class="fas fa-paper-plane"></i> Eenvoudig en snel
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Community Impact Section -->
    <section class="section community-impact">
        <div class="container">
            <div class="section-title">
                <h2>Onze Impact in de Gemeenschap</h2>
                <p>Samen maken we Tilburg digitaal inclusiever - bekijk wat we hebben bereikt</p>
            </div>
            <div class="impact-stats">
                <div class="impact-stat">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">Mensen Geholpen</div>
                        <div class="stat-description">Van senioren tot ondernemers</div>
                    </div>
                </div>

                <div class="impact-stat">
                    <div class="stat-icon">
                        <i class="fas fa-laptop"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">150+</div>
                        <div class="stat-label">Computers Gered</div>
                        <div class="stat-description">Oude computers nieuw leven gegeven</div>
                    </div>
                </div>

                <div class="impact-stat">
                    <div class="stat-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">75+</div>
                        <div class="stat-label">Workshops Gegeven</div>
                        <div class="stat-description">Digitale vaardigheden geleerd</div>
                    </div>
                </div>

                <div class="impact-stat">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">2000+</div>
                        <div class="stat-label">Vrijwilligersuren</div>
                        <div class="stat-description">Tijd geïnvesteerd in de gemeenschap</div>
                    </div>
                </div>
            </div>

            <div class="impact-stories">
                <h3>Succesverhalen uit de Gemeenschap</h3>
                <div class="stories-grid">
                    <div class="story-card">
                        <div class="story-quote">
                            <i class="fas fa-quote-left"></i>
                            <p>"Dankzij ICT Tilburg kan ik nu videobellen met mijn kleinkinderen in Canada. Ze hebben me geduldig alles uitgelegd."</p>
                        </div>
                        <div class="story-author">
                            <strong>Maria, 78 jaar</strong>
                            <span>Deelnemer Computer Basis Workshop</span>
                        </div>
                    </div>

                    <div class="story-card">
                        <div class="story-quote">
                            <i class="fas fa-quote-left"></i>
                            <p>"Mijn 10 jaar oude laptop werkt nu sneller dan ooit dankzij Linux. En het kostte me niets!"</p>
                        </div>
                        <div class="story-author">
                            <strong>Ahmed, student</strong>
                            <span>Linux Installatie Service</span>
                        </div>
                    </div>

                    <div class="story-card">
                        <div class="story-quote">
                            <i class="fas fa-quote-left"></i>
                            <p>"Ze hebben een complete website voor onze voetbalvereniging gemaakt. Professioneel en helemaal gratis!"</p>
                        </div>
                        <div class="story-author">
                            <strong>Peter, voorzitter VV Tilburg</strong>
                            <span>Website Ontwikkeling</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="section cta-section">
        <div class="container">
            <div class="cta-content">
                <div class="cta-text">
                    <h2>Sluit Je Aan Bij Onze Missie</h2>
                    <p>Of je nu hulp nodig hebt of wilt helpen - iedereen is welkom bij ICT Tilburg. Samen maken we technologie toegankelijk voor iedereen in onze gemeenschap.</p>
                    <div class="cta-options">
                        <div class="cta-option">
                            <i class="fas fa-question-circle"></i>
                            <h4>Hulp Nodig?</h4>
                            <p>Neem contact op voor gratis IT ondersteuning</p>
                            <a href="contact.html" class="btn btn-primary">Contact Opnemen</a>
                        </div>
                        <div class="cta-option">
                            <i class="fas fa-hands-helping"></i>
                            <h4>Wil Je Helpen?</h4>
                            <p>Word vrijwilliger en maak het verschil</p>
                            <a href="contact.html" class="btn btn-secondary">Word Vrijwilliger</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Gratis, innovatieve IT-ondersteuning in Tilburg en omgeving.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Snel naar</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="over-ons.html">Over Ons</a></li>
                        <li><a href="diensten.html">Diensten</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog & Nieuws</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Onze Diensten</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="bedrijfsbeheer.html">Bedrijfsbeheer</a></li>
                        <li><a href="ai-automatisering.html">AI & Automatisering</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nieuwsbrief</h3>
                    <p>Meld je aan voor nieuws, tips en uitnodigingen voor gratis workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Jouw e-mail" required>
                        </div>
                        <button type="submit" class="btn">Aanmelden</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>&copy; 2025 ICT Tilburg. Alle rechten voorbehouden. | Technologie voor iedereen, gemeenschap op één.</p>
                </div>
                <div class="footer-legal">
                    <a href="privacy.html">Privacybeleid</a>
                    <a href="terms-diensten.html">Servicevoorwaarden</a>
                    <a href="terms-gebruik.html">Gebruiksvoorwaarden</a>
                </div>
            </div>
        </div>
    </footer>

</body>
</html>