<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact - ICT Tilburg | Gratis IT Ondersteuning</title>
    <meta name="description" content="Neem contact op met ICT Tilburg voor gratis IT ondersteuning. WhatsApp, e-mail, telefoon of kom langs. Wij helpen u graag met al uw technische vragen!">
    <meta name="keywords" content="ICT Tilburg contact, gratis IT hulp, technische ondersteuning, WhatsApp support, computer hulp Tilburg">
    <meta name="author" content="ICT Tilburg">
    <meta property="og:title" content="Contact - ICT Tilburg | Gratis IT Ondersteuning">
    <meta property="og:description" content="Neem contact op met ICT Tilburg voor gratis IT ondersteuning. Meerdere contactmogelijkheden beschikbaar.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://icttilburg.nl/nl/contact.html">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Contact - ICT Tilburg">
    <meta name="twitter:description" content="Gratis IT ondersteuning voor de Tilburg gemeenschap. Neem contact op via WhatsApp, e-mail of telefoon.">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #ff6b35;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: var(--font-main); color: var(--dark); line-height: 1.6; background-color: #f0f4f8; }
        .container { width: 90%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
        .lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .main-header .container { display: flex; align-items: center; justify-content: space-between; }
        nav { display: flex; align-items: center; justify-content: space-between; width: 100%; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo-link { display: flex; align-items: center; gap: 15px; text-decoration: none; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        .nav-links { display: flex; list-style: none; gap: 25px; margin: 0; padding: 0; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
        .section { padding: 80px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
        .contact-page-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 50px; }
        .contact-form { background-color: #f8f9fa; padding: 30px; border-radius: 10px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-control { width: 100%; padding: 12px 15px; border: 1px solid #ddd; border-radius: 5px; font-family: var(--font-main); font-size: 1rem; }
        textarea.form-control { min-height: 120px; resize: vertical; }
        .btn { display: inline-block; background-color: var(--primary); color: white; padding: 12px 30px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 1rem; transition: all 0.3s; border: 2px solid var(--primary); cursor: pointer; }
        .btn:hover { background-color: var(--secondary); border-color: var(--secondary); box-shadow: 0 5px 15px rgba(0,0,0,0.15); }
        .btn-primary { background-color: var(--primary); border-color: var(--primary); }
        .btn-primary:hover { background-color: var(--secondary); border-color: var(--secondary); }
        .btn-secondary { background-color: var(--gray); border-color: var(--gray); }
        .btn-secondary:hover { background-color: var(--dark); border-color: var(--dark); }
        .hero-buttons { display: flex; gap: 20px; margin-top: 30px; }
        .hero-buttons .btn { display: flex; align-items: center; gap: 8px; }
        .contact-details-block { background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.05);}
        .details-list { list-style: none; padding: 0; margin: 0 0 32px 0;}
        .details-list li { display: flex; gap: 15px; margin-bottom: 22px; align-items: flex-start;}
        .details-list .icon { width: 38px; height: 38px; border-radius: 50%; background-color: rgba(44,94,146,0.07); display: flex; align-items: center; justify-content: center; font-size: 1.2rem; color: var(--primary);}
        .contact-tools { margin-top: 25px;}
        .contact-tools-title { font-weight: bold; margin-bottom: 12px; color: var(--primary);}
        .contact-tools-list { display: flex; flex-wrap: wrap; gap: 13px;}
        .contact-tool-link { display: flex; align-items: center; gap: 8px; background: #f0f4f8; border-radius: 50px; padding: 8px 18px; text-decoration: none; color: var(--dark); font-weight: 600; transition: background 0.2s;}
        .contact-tool-link:hover { background: var(--secondary); color: white;}
        .map-section { margin-top: 40px;}
        .map-title { font-weight: 700; color: var(--primary); margin-bottom: 18px;}
        .map-frame { border: none; width: 100%; height: 260px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.07);}
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
        /* Enhanced Contact Styles */

        /* Hero Section */
        .contact-hero {
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
            color: white;
            padding: 100px 0;
        }

        .contact-hero .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .contact-hero .hero-subtitle {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .contact-hero .hero-description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .contact-hero .hero-stats {
            display: flex;
            gap: 30px;
            margin-top: 40px;
        }

        .contact-hero .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            line-height: 1;
        }

        .contact-hero .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .contact-hero .hero-image img {
            width: 100%;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        /* Contact Methods */
        .contact-methods {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .contact-methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .contact-method {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            border: 2px solid transparent;
            text-align: center;
        }

        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .contact-method.featured {
            border-color: var(--secondary);
        }

        .method-badge {
            position: absolute;
            top: -10px;
            right: 20px;
            background: var(--secondary);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .method-icon {
            width: 80px;
            height: 80px;
            background: var(--light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--primary);
            font-size: 2rem;
        }

        .contact-method.featured .method-icon {
            background: var(--primary);
            color: white;
        }

        .method-content h3 {
            color: var(--dark);
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .method-content p {
            color: var(--gray);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .method-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 20px 0;
        }

        .detail-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.9rem;
            color: var(--gray);
        }

        .detail-item i {
            color: var(--primary);
        }

        /* Contact Form Section */
        .contact-form-section {
            padding: 80px 0;
        }

        .contact-form-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 60px;
            align-items: start;
        }

        .form-intro h2 {
            color: var(--primary);
            margin-bottom: 20px;
        }

        .form-benefits {
            margin-top: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-benefit {
            display: flex;
            align-items: center;
            gap: 15px;
            color: var(--gray);
        }

        .form-benefit i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .enhanced-contact-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .contact-form .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .contact-form .form-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 8px;
        }

        .contact-form .form-label i {
            color: var(--primary);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .form-note {
            margin-top: 20px;
            padding: 15px;
            background: var(--light);
            border-radius: 8px;
            font-size: 0.9rem;
            color: var(--gray);
        }

        .form-note i {
            color: var(--primary);
            margin-right: 8px;
        }

        .form-note a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
        }

        .form-note a:hover {
            text-decoration: underline;
        }

        /* Location & Hours */
        .location-hours {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .location-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: start;
        }

        .location-info h2 {
            color: var(--primary);
            margin-bottom: 20px;
        }

        .location-details {
            margin-top: 40px;
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .location-detail {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .detail-icon {
            width: 50px;
            height: 50px;
            background: var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .detail-content h3 {
            color: var(--dark);
            margin-bottom: 10px;
        }

        .detail-content p {
            color: var(--gray);
            line-height: 1.6;
            margin: 0;
        }

        .hours-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .hours-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .hours-item.current-day {
            background: var(--light);
            padding: 8px 15px;
            border-radius: 8px;
            border-bottom: none;
            font-weight: 600;
            color: var(--primary);
        }

        .day {
            font-weight: 500;
        }

        .time {
            color: var(--gray);
        }

        .current-day .time {
            color: var(--primary);
        }

        .location-map {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .map-container {
            position: relative;
        }

        .map-frame {
            width: 100%;
            height: 400px;
            border: none;
            border-radius: 10px;
        }

        .map-links {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            justify-content: center;
        }

        .map-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            padding: 8px 15px;
            border: 2px solid var(--primary);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .map-link:hover {
            background: var(--primary);
            color: white;
        }

        /* FAQ Section */
        .contact-faq {
            padding: 80px 0;
        }

        .faq-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .faq-item {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .faq-item:hover {
            transform: translateY(-3px);
        }

        .faq-question h3 {
            color: var(--primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .faq-answer p {
            color: var(--gray);
            line-height: 1.6;
            margin: 0;
        }

        /* Emergency Contact */
        .emergency-contact {
            background: var(--primary);
            color: white;
            padding: 60px 0;
        }

        .emergency-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .emergency-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .emergency-content h2 {
            color: white;
            margin-bottom: 15px;
        }

        .emergency-content p {
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .emergency-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .emergency-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
        }

        .emergency-btn:hover {
            background: white;
            color: var(--primary);
        }

        .emergency-note {
            font-size: 0.9rem;
            opacity: 0.8;
            margin: 0;
        }

        .emergency-note i {
            margin-right: 8px;
        }

        /* Mobile Navigation */
        @media (max-width: 992px) {
            .mobile-menu-btn {
                display: block;
                order: 3;
            }

            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                flex-direction: column;
                padding: 20px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                z-index: 1000;
            }

            .nav-links.show {
                display: flex;
            }

            .nav-links li {
                margin: 10px 0;
            }

            nav {
                position: relative;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .contact-hero .hero-content,
            .contact-form-container,
            .location-container {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .contact-methods-grid {
                grid-template-columns: 1fr;
            }

            .contact-form .form-row {
                grid-template-columns: 1fr;
            }

            .emergency-actions {
                flex-direction: column;
                align-items: center;
            }

            .map-links {
                flex-direction: column;
            }

            .faq-grid {
                grid-template-columns: 1fr;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .hero-stats {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
        }

        @media (max-width: 992px) {
            .contact-page-grid, .footer-grid { grid-template-columns: 1fr; }
        }
    </style>
    <script src="../assets/js/common.js"></script>
    <link rel="alternate" hreflang="en" href="../en/contact.html">
    <link rel="alternate" hreflang="pt" href="../pt/contacto.html">
    <link rel="canonical" href="contact.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="Email ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn" aria-pressed="false" data-lang="en" data-href="../en/contact.html">EN</button>
                    <button class="lang-btn active" aria-pressed="true" data-lang="nl" data-href="contact.html">NL</button>
                    <button class="lang-btn" aria-pressed="false" data-lang="pt" data-href="../pt/contacto.html">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <nav aria-label="Main navigation">
                    <div class="logo-area">
                        <a href="index.html" class="logo-link">
                            <div class="logo" aria-label="Logo">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <div class="logo-text">ICT<span>Tilburg</span></div>
                        </a>
                    </div>

                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>

                    <ul class="nav-links">
                        <li><a href="index.html" class="nav-item">Home</a></li>
                        <li><a href="over-ons.html" class="nav-item">Over Ons</a></li>
                        <li><a href="diensten.html" class="nav-item">Diensten</a></li>
                        <li><a href="workshops.html" class="nav-item">Workshops</a></li>
                        <li><a href="blog.html" class="nav-item">Blog & Nieuws</a></li>
                        <li><a href="forum.html" class="nav-item">Forum</a></li>
                        <li><a href="downloads.html" class="nav-item">Downloads</a></li>
                        <li><a href="partners.html" class="nav-item">Partners</a></li>
                        <li><a href="contact.html" class="nav-item">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
    <!-- Hero Section -->
    <section class="hero contact-hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>Neem Contact Met Ons Op</h1>
                    <p class="hero-subtitle">Wij staan klaar om u te helpen</p>
                    <p class="hero-description">Heeft u een vraag, technisch probleem of wilt u een afspraak maken? Kies de manier die het beste bij u past. Wij reageren altijd snel en vriendelijk!</p>
                    <div class="hero-buttons">
                        <a href="#contact-form" class="btn btn-primary">
                            <i class="fas fa-envelope"></i> Stuur Bericht
                        </a>
                        <a href="tel:+31612345678" class="btn btn-secondary">
                            <i class="fas fa-phone"></i> Direct Bellen
                        </a>
                    </div>
                    <div class="hero-stats">
                        <div class="stat">
                            <div class="stat-number">24u</div>
                            <div class="stat-label">Reactietijd</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">6</div>
                            <div class="stat-label">Contact Opties</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Gratis Hulp</div>
                        </div>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="Contact en communicatie" loading="lazy">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Methods Section -->
    <section class="section contact-methods">
        <div class="container">
            <div class="section-title">
                <h2>Kies Uw Voorkeursmanier</h2>
                <p>Verschillende manieren om contact met ons op te nemen - kies wat het beste bij u past</p>
            </div>
            <div class="contact-methods-grid">
                <div class="contact-method featured">
                    <div class="method-badge">Populair</div>
                    <div class="method-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="method-content">
                        <h3>WhatsApp</h3>
                        <p>Snelle hulp via WhatsApp. Stuur een bericht en krijg direct antwoord.</p>
                        <div class="method-details">
                            <span class="detail-item"><i class="fas fa-clock"></i> Reactie binnen 1 uur</span>
                            <span class="detail-item"><i class="fas fa-image"></i> Screenshots mogelijk</span>
                        </div>
                        <a href="https://web.whatsapp.com/send?phone=31612345678&text=Hallo%20ICT%20Tilburg,%20ik%20heb%20een%20vraag" target="_blank" class="btn btn-primary">
                            <i class="fab fa-whatsapp"></i> Start WhatsApp Chat
                        </a>
                    </div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="method-content">
                        <h3>E-mail</h3>
                        <p>Uitgebreide vragen? Stuur een e-mail met alle details.</p>
                        <div class="method-details">
                            <span class="detail-item"><i class="fas fa-clock"></i> Reactie binnen 24 uur</span>
                            <span class="detail-item"><i class="fas fa-paperclip"></i> Bijlagen mogelijk</span>
                        </div>
                        <a href="mailto:<EMAIL>?subject=Hulpvraag&body=Hallo%20ICT%20Tilburg,%0A%0AIk%20heb%20een%20vraag%20over:" class="btn btn-primary">
                            <i class="fas fa-envelope"></i> Stuur E-mail
                        </a>
                    </div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="method-content">
                        <h3>Telefoon</h3>
                        <p>Direct persoonlijk contact voor urgente vragen.</p>
                        <div class="method-details">
                            <span class="detail-item"><i class="fas fa-clock"></i> Ma-Vr 9:00-17:00</span>
                            <span class="detail-item"><i class="fas fa-user"></i> Persoonlijk contact</span>
                        </div>
                        <a href="tel:+31612345678" class="btn btn-primary">
                            <i class="fas fa-phone"></i> Bel Direct
                        </a>
                    </div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="method-content">
                        <h3>Video Call</h3>
                        <p>Scherm delen voor directe hulp bij computerproblemen.</p>
                        <div class="method-details">
                            <span class="detail-item"><i class="fas fa-calendar"></i> Op afspraak</span>
                            <span class="detail-item"><i class="fas fa-desktop"></i> Scherm delen</span>
                        </div>
                        <a href="#contact-form" class="btn btn-primary">
                            <i class="fas fa-calendar-plus"></i> Afspraak Maken
                        </a>
                    </div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="method-content">
                        <h3>Kom Langs</h3>
                        <p>Bezoek ons in het wijkcentrum voor persoonlijke hulp.</p>
                        <div class="method-details">
                            <span class="detail-item"><i class="fas fa-clock"></i> Di & Do 14:00-16:00</span>
                            <span class="detail-item"><i class="fas fa-coffee"></i> Informele sfeer</span>
                        </div>
                        <a href="#location-info" class="btn btn-primary">
                            <i class="fas fa-map-marker-alt"></i> Bekijk Locatie
                        </a>
                    </div>
                </div>

                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="method-content">
                        <h3>Forum</h3>
                        <p>Stel uw vraag in ons community forum.</p>
                        <div class="method-details">
                            <span class="detail-item"><i class="fas fa-users"></i> Community hulp</span>
                            <span class="detail-item"><i class="fas fa-search"></i> Doorzoekbaar</span>
                        </div>
                        <a href="forum.html" class="btn btn-primary">
                            <i class="fas fa-comments"></i> Naar Forum
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="section contact-form-section" id="contact-form">
        <div class="container">
            <div class="contact-form-container">
                <div class="form-intro">
                    <h2>Stuur Ons Een Bericht</h2>
                    <p>Vul het formulier in en we nemen zo snel mogelijk contact met u op. Alle velden zijn optioneel, maar hoe meer informatie u geeft, hoe beter we u kunnen helpen.</p>
                    <div class="form-benefits">
                        <div class="form-benefit">
                            <i class="fas fa-clock"></i>
                            <span>Reactie binnen 24 uur</span>
                        </div>
                        <div class="form-benefit">
                            <i class="fas fa-shield-alt"></i>
                            <span>Uw gegevens zijn veilig</span>
                        </div>
                        <div class="form-benefit">
                            <i class="fas fa-gift"></i>
                            <span>Gratis advies en hulp</span>
                        </div>
                    </div>
                </div>

                <div class="enhanced-contact-form">
                    <form class="contact-form" method="POST" action="#">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="contact-name">
                                    <i class="fas fa-user"></i> Uw naam
                                </label>
                                <input type="text" id="contact-name" name="contact-name" class="form-control" placeholder="Bijv. Anna de Vries" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="contact-email">
                                    <i class="fas fa-envelope"></i> E-mail adres
                                </label>
                                <input type="email" id="contact-email" name="contact-email" class="form-control" placeholder="<EMAIL>" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="contact-phone">
                                    <i class="fas fa-phone"></i> Telefoonnummer (optioneel)
                                </label>
                                <input type="tel" id="contact-phone" name="contact-phone" class="form-control" placeholder="06 1234 5678">
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="contact-urgency">
                                    <i class="fas fa-exclamation-circle"></i> Urgentie
                                </label>
                                <select id="contact-urgency" name="contact-urgency" class="form-control">
                                    <option value="low">Geen haast</option>
                                    <option value="medium">Binnen een week</option>
                                    <option value="high">Zo snel mogelijk</option>
                                    <option value="urgent">Urgent - vandaag nog</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="contact-category">
                                <i class="fas fa-folder"></i> Onderwerp categorie
                            </label>
                            <select id="contact-category" name="contact-category" class="form-control" required>
                                <option value="">Kies een categorie</option>
                                <option value="technical-support">Technische ondersteuning</option>
                                <option value="workshop">Workshop aanvragen</option>
                                <option value="partnership">Samenwerking</option>
                                <option value="general">Algemene vraag</option>
                                <option value="feedback">Feedback</option>
                                <option value="other">Anders</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="contact-subject">
                                <i class="fas fa-heading"></i> Onderwerp
                            </label>
                            <input type="text" id="contact-subject" name="contact-subject" class="form-control" placeholder="Korte beschrijving van uw vraag" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="contact-message">
                                <i class="fas fa-comment"></i> Uw bericht
                            </label>
                            <textarea id="contact-message" name="contact-message" class="form-control" rows="6" placeholder="Beschrijf uw vraag of probleem zo duidelijk mogelijk. Welk apparaat gebruikt u? Wat probeert u te doen? Wat gaat er mis?" required></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="contact-device">
                                <i class="fas fa-laptop"></i> Welk apparaat gebruikt u? (optioneel)
                            </label>
                            <input type="text" id="contact-device" name="contact-device" class="form-control" placeholder="Bijv. Windows laptop, iPhone, Android tablet">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Verstuur Bericht
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> Opnieuw
                            </button>
                        </div>
                        <div class="form-note">
                            <p><i class="fas fa-info-circle"></i> We behandelen uw gegevens vertrouwelijk volgens ons <a href="privacy.html">privacybeleid</a>. U ontvangt binnen 24 uur een reactie van ons team.</p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Location & Hours Section -->
    <section class="section location-hours" id="location-info">
        <div class="container">
            <div class="location-container">
                <div class="location-info">
                    <h2>Bezoek Ons</h2>
                    <p>Kom langs voor persoonlijke hulp in een gezellige, informele sfeer. Geen afspraak nodig tijdens onze openingstijden!</p>

                    <div class="location-details">
                        <div class="location-detail">
                            <div class="detail-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="detail-content">
                                <h3>Adres</h3>
                                <p>Wijkcentrum Tilburg<br>
                                Heuvelring 122<br>
                                5038 CL Tilburg</p>
                            </div>
                        </div>

                        <div class="location-detail">
                            <div class="detail-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="detail-content">
                                <h3>Openingstijden</h3>
                                <div class="hours-list">
                                    <div class="hours-item">
                                        <span class="day">Maandag</span>
                                        <span class="time">Gesloten</span>
                                    </div>
                                    <div class="hours-item current-day">
                                        <span class="day">Dinsdag</span>
                                        <span class="time">14:00 - 16:00</span>
                                    </div>
                                    <div class="hours-item">
                                        <span class="day">Woensdag</span>
                                        <span class="time">Gesloten</span>
                                    </div>
                                    <div class="hours-item current-day">
                                        <span class="day">Donderdag</span>
                                        <span class="time">14:00 - 16:00</span>
                                    </div>
                                    <div class="hours-item">
                                        <span class="day">Vrijdag</span>
                                        <span class="time">Gesloten</span>
                                    </div>
                                    <div class="hours-item">
                                        <span class="day">Weekend</span>
                                        <span class="time">Gesloten</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="location-detail">
                            <div class="detail-icon">
                                <i class="fas fa-bus"></i>
                            </div>
                            <div class="detail-content">
                                <h3>Openbaar Vervoer</h3>
                                <p>Bushalte "Heuvelring" (lijn 3, 5, 7)<br>
                                5 minuten lopen vanaf station Tilburg<br>
                                Gratis parkeren beschikbaar</p>
                            </div>
                        </div>

                        <div class="location-detail">
                            <div class="detail-icon">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="detail-content">
                                <h3>Wat Te Verwachten</h3>
                                <p>Informele sfeer met koffie/thee<br>
                                Persoonlijke aandacht<br>
                                Breng uw eigen apparaat mee</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="location-map">
                    <div class="map-container">
                        <iframe class="map-frame"
                            src="https://www.openstreetmap.org/export/embed.html?bbox=5.0720,51.5580,5.0850,51.5650&amp;layer=mapnik&amp;marker=51.5615,5.0785"
                            allowfullscreen="" loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade"
                            title="Kaart ICT Tilburg"></iframe>
                        <div class="map-links">
                            <a href="https://www.openstreetmap.org/?mlat=51.5615&mlon=5.0785#map=17/51.5615/5.0785" target="_blank" class="map-link">
                                <i class="fas fa-external-link-alt"></i> Bekijk grote kaart
                            </a>
                            <a href="https://www.google.com/maps/dir//Heuvelring+122,+5038+CL+Tilburg" target="_blank" class="map-link">
                                <i class="fas fa-route"></i> Routebeschrijving
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="section contact-faq">
        <div class="container">
            <div class="section-title">
                <h2>Veelgestelde Vragen</h2>
                <p>Antwoorden op de meest gestelde vragen over contact en ondersteuning</p>
            </div>
            <div class="faq-grid">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3><i class="fas fa-question-circle"></i> Hoe snel krijg ik antwoord?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>We streven ernaar om binnen 24 uur te reageren op e-mails en formulieren. WhatsApp berichten beantwoorden we meestal binnen 1-2 uur tijdens kantooruren.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3><i class="fas fa-question-circle"></i> Kost de hulp geld?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>Nee, alle ondersteuning van ICT Tilburg is 100% gratis. We zijn een non-profit organisatie die zich inzet voor digitale inclusie in Tilburg.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3><i class="fas fa-question-circle"></i> Kan ik zonder afspraak langskomen?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>Ja, tijdens onze openingstijden (dinsdag en donderdag 14:00-16:00) kunt u zonder afspraak langskomen. Voor uitgebreide hulp raden we aan om vooraf contact op te nemen.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3><i class="fas fa-question-circle"></i> Welke apparaten kunnen jullie helpen?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>We helpen met alle soorten apparaten: computers (Windows, Mac, Linux), smartphones, tablets, smart TV's en andere digitale apparaten. Ook software en online diensten.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3><i class="fas fa-question-circle"></i> Kunnen jullie op afstand helpen?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>Ja, we bieden remote ondersteuning via TeamViewer, Chrome Remote Desktop of andere veilige verbindingen. We kunnen uw scherm bekijken en problemen oplossen zonder dat u naar ons toe hoeft te komen.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3><i class="fas fa-question-circle"></i> Wat als mijn probleem te ingewikkeld is?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>Er is geen probleem te ingewikkeld! Als we het niet direct kunnen oplossen, zoeken we het uit of verwijzen we u door naar gespecialiseerde hulp. We laten u nooit in de steek.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Emergency Contact Section -->
    <section class="section emergency-contact">
        <div class="container">
            <div class="emergency-card">
                <div class="emergency-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="emergency-content">
                    <h2>Urgente Hulp Nodig?</h2>
                    <p>Voor urgente technische problemen die niet kunnen wachten, kunt u ons direct bereiken via WhatsApp of telefoon.</p>
                    <div class="emergency-actions">
                        <a href="https://web.whatsapp.com/send?phone=31612345678&text=URGENT:%20" target="_blank" class="btn btn-primary emergency-btn">
                            <i class="fab fa-whatsapp"></i> WhatsApp Direct
                        </a>
                        <a href="tel:+31612345678" class="btn btn-secondary emergency-btn">
                            <i class="fas fa-phone"></i> Bel Nu
                        </a>
                    </div>
                    <p class="emergency-note">
                        <i class="fas fa-info-circle"></i> Beschikbaar tijdens kantooruren (ma-vr 9:00-17:00). Voor echte noodgevallen proberen we ook buiten deze tijden te helpen.
                    </p>
                </div>
            </div>
        </div>
    </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Gratis, innovatieve IT-ondersteuning in Tilburg en omgeving.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Snel naar</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="over-ons.html">Over Ons</a></li>
                        <li><a href="diensten.html">Diensten</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog & Nieuws</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Onze Diensten</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="bedrijfsbeheer.html">Bedrijfsbeheer</a></li>
                        <li><a href="ai-automatisering.html">AI & Automatisering</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nieuwsbrief</h3>
                    <p>Meld je aan voor nieuws, tips en uitnodigingen voor gratis workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Jouw e-mail" required>
                        </div>
                        <button type="submit" class="btn">Aanmelden</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>&copy; 2025 ICT Tilburg. Alle rechten voorbehouden. | Technologie voor iedereen, gemeenschap op één.</p>
                </div>
                <div class="footer-legal">
                    <a href="privacy.html">Privacybeleid</a>
                    <a href="terms-diensten.html">Servicevoorwaarden</a>
                    <a href="terms-gebruik.html">Gebruiksvoorwaarden</a>
                </div>
            </div>
        </div>
    </footer>
    <script>
        // Mobile Menu Toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('nav ul');
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                navMenu.classList.toggle('show');
            });
        }
        // Language Switcher
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(button => {
            button.addEventListener('click', () => {
                langButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                alert('Taal gewijzigd naar ' + button.textContent);
            });
        });
        // Form Submission
        const contactForm = document.querySelector('.contact-form form');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Bedankt voor uw bericht! We nemen spoedig contact op.');
                this.reset();
            });
        }
    </script>
</body>
</html>