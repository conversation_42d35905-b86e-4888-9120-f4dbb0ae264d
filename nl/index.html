<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Gratis IT Ondersteuning & Digitale Inclusie | Vrijwilligers voor Technologie</title>
    <meta name="description" content="ICT Tilburg biedt gratis IT ondersteuning, workshops en technische hulp voor iedereen in Tilburg. Vrijwilligers die technologie toegankelijk maken voor senioren, bedrijven en de gemeenschap.">
    <meta name="keywords" content="ICT Tilburg, gratis IT support, digitale inclusie, workshops, Linux, open source, AI, website ontwikkeling, vrijwilligers, Tilburg">
    <meta name="author" content="ICT Tilburg">
    <meta property="og:title" content="ICT Tilburg - Gratis IT Ondersteuning & Digitale Inclusie">
    <meta property="og:description" content="Vrijwilligers die technologie toegankelijk maken voor iedereen in Tilburg. Gratis IT support, workshops en innovatieve oplossingen.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://icttilburg.nl">
    <meta property="og:image" content="https://icttilburg.nl/assets/images/og-image.jpg">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="ICT Tilburg - Gratis IT Ondersteuning">
    <meta name="twitter:description" content="Vrijwilligers die technologie toegankelijk maken voor iedereen in Tilburg.">
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/footer-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #1e40af;
            --primary-dark: #1e3a8a;
            --secondary: #10b981;
            --secondary-dark: #059669;
            --accent: #f59e0b;
            --accent-dark: #d97706;
            --light: #f8fafc;
            --light-blue: #eff6ff;
            --dark: #1f2937;
            --gray: #6b7280;
            --gray-light: #9ca3af;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary) 0%, var(--secondary-dark) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --font-main: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-main);
            color: var(--dark);
            line-height: 1.6;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            overflow-x: hidden;
        }

        /* Smooth animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: var(--transition);
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }
        .container {
            width: 90%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Enhanced Header */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .header-top {
            background: var(--gradient-primary);
            color: white;
            padding: 10px 0;
            font-size: 0.9rem;
        }

        .header-top .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .contact-info {
            display: flex;
            gap: 25px;
        }

        .contact-info a {
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
            opacity: 0.9;
        }

        .contact-info a:hover {
            opacity: 1;
            transform: translateY(-1px);
        }

        .language-selector {
            display: flex;
            gap: 5px;
        }

        .lang-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            font-size: 0.85rem;
            padding: 6px 12px;
            border-radius: 20px;
            transition: var(--transition);
            font-weight: 500;
        }

        .lang-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .lang-btn.active {
            background: white;
            color: var(--primary);
            font-weight: 600;
        }

        .main-header {
            padding: 20px 0;
            background: white;
        }

        .main-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-area {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 65px;
            height: 65px;
            background: var(--gradient-secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .logo:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-lg);
        }

        .logo-text {
            font-family: var(--font-heading);
            font-weight: 700;
            font-size: 2rem;
            color: var(--primary);
            letter-spacing: -0.5px;
        }

        .logo-text span {
            color: var(--secondary);
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 30px;
            align-items: center;
        }

        nav a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            font-size: 1rem;
            transition: var(--transition);
            position: relative;
            padding: 8px 16px;
            border-radius: var(--border-radius);
        }

        nav a:hover {
            color: var(--primary);
            background: var(--light-blue);
            transform: translateY(-1px);
        }

        nav a.active {
            color: var(--primary);
            background: var(--light-blue);
            font-weight: 600;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--primary);
            padding: 8px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .mobile-menu-btn:hover {
            background: var(--light-blue);
        }

        /* Add space for fixed header */
        body {
            padding-top: 140px;
        }
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
    </style>
    <!-- SEO Meta tags -->
    <meta name="description" content="ICT Tilburg biedt professionele IT-ondersteuning en innovatieve oplossingen voor bedrijven en particulieren in Tilburg en omgeving.">
    <meta name="keywords" content="ICT Tilburg, IT support, IT diensten, technologie, innovatie, workshops, Tilburg">
    <script src="../assets/js/common.js"></script>
    <!-- Language alternatives -->
    <link rel="alternate" hreflang="en" href="../en/index.html">
    <link rel="alternate" hreflang="pt" href="../pt/index.html">
    <link rel="canonical" href="index.html">

    <!-- Enhanced Homepage Styles -->
    <style>
        /* Enhanced Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--light) 0%, var(--light-blue) 50%, var(--light) 100%);
            padding: 100px 0 120px;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            opacity: 0.5;
            animation: float 6s ease-in-out infinite;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            min-height: 70vh;
            position: relative;
            z-index: 2;
        }

        .hero-text {
            animation: fadeInLeft 1s ease-out;
        }

        .hero h1 {
            font-family: var(--font-heading);
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            color: var(--dark);
            margin-bottom: 25px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .hero-description {
            font-size: 1.2rem;
            line-height: 1.7;
            margin-bottom: 40px;
            color: var(--gray);
            max-width: 90%;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            margin-bottom: 50px;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 16px 32px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
            border: 2px solid transparent;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary);
            border-color: var(--primary);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .hero-stats {
            display: flex;
            gap: 40px;
            margin-top: 40px;
        }

        .stat {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .stat:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 1rem;
            color: var(--gray);
            font-weight: 500;
        }

        .hero-image {
            animation: fadeInRight 1s ease-out;
            position: relative;
        }

        .hero-image img {
            width: 100%;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            transition: var(--transition);
        }

        .hero-image:hover img {
            transform: scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .hero-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            background: var(--gradient-secondary);
            border-radius: var(--border-radius-lg);
            z-index: -1;
            opacity: 0.1;
        }

        /* Enhanced Sections */
        .section {
            padding: 100px 0;
            position: relative;
        }

        .section-title {
            text-align: center;
            margin-bottom: 70px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .section-title h2 {
            font-family: var(--font-heading);
            font-size: 3rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 20px;
            position: relative;
            line-height: 1.2;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--gradient-secondary);
            border-radius: 2px;
        }

        .section-title p {
            font-size: 1.3rem;
            color: var(--gray);
            line-height: 1.6;
            margin-top: 25px;
        }

        /* Community Impact Section */
        .community-impact {
            background: linear-gradient(135deg, var(--light) 0%, var(--light-blue) 100%);
            position: relative;
            overflow: hidden;
        }

        .community-impact::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(30, 64, 175, 0.05) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        .impact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
            margin-top: 70px;
            position: relative;
            z-index: 2;
        }

        .impact-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 40px 30px;
            border-radius: var(--border-radius-lg);
            text-align: center;
            box-shadow: var(--shadow-lg);
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .impact-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-secondary);
        }

        .impact-item:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-xl);
        }

        .impact-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            color: white;
            font-size: 2rem;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .impact-item:hover .impact-icon {
            transform: scale(1.1);
            animation: pulse 1s ease-in-out;
        }

        .impact-item h3 {
            font-family: var(--font-heading);
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 15px;
        }

        .impact-item p {
            color: var(--gray);
            line-height: 1.6;
            font-size: 1.1rem;
        }

        /* Enhanced Services Section */
        .services-section {
            background: white;
            position: relative;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-top: 70px;
        }

        .service-card {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: 40px 30px;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            position: relative;
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-secondary);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-xl);
        }

        .service-card.featured {
            border: 2px solid var(--primary);
            position: relative;
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.02) 0%, rgba(16, 185, 129, 0.02) 100%);
        }

        .service-card.featured::after {
            content: "Populair";
            position: absolute;
            top: -12px;
            right: 25px;
            background: var(--gradient-primary);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            box-shadow: var(--shadow-md);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
            color: white;
            font-size: 2rem;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .service-card:hover .service-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .service-content h3 {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .service-content p {
            color: var(--gray);
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 1.1rem;
        }

        .service-features {
            display: flex;
            gap: 12px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .feature-tag {
            background: var(--light-blue);
            color: var(--primary);
            padding: 6px 16px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            border: 1px solid rgba(30, 64, 175, 0.1);
            transition: var(--transition);
        }

        .feature-tag:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-2px);
        }

        .services-cta {
            text-align: center;
            margin-top: 80px;
            padding: 50px 40px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius-lg);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .services-cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="3" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="2" fill="white" opacity="0.1"/></svg>');
        }

        .services-cta h3 {
            font-family: var(--font-heading);
            font-size: 2rem;
            font-weight: 600;
            color: white;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }

        .services-cta p {
            font-size: 1.2rem;
            color: white;
            opacity: 0.9;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .services-cta .btn {
            background: white;
            color: var(--primary);
            border: none;
            position: relative;
            z-index: 2;
        }

        .services-cta .btn:hover {
            background: var(--light);
            transform: translateY(-3px);
        }

        /* Achievements Section */
        .achievements {
            background: var(--primary);
            color: white;
        }

        .achievements .section-title h2,
        .achievements .section-title p {
            color: white;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .achievement-card {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .achievement-icon {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            color: white;
            font-size: 1.5rem;
        }

        .achievement-date {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-top: 15px;
        }

        /* Workshop Section Enhancements */
        .workshops-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .workshop-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            position: relative;
            transition: transform 0.3s ease;
        }

        .workshop-card:hover {
            transform: translateY(-5px);
        }

        .workshop-card.upcoming {
            border: 2px solid var(--secondary);
        }

        .workshop-badge {
            position: absolute;
            top: -10px;
            right: 20px;
            background: var(--secondary);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .workshop-icon {
            width: 60px;
            height: 60px;
            background: var(--light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            color: var(--primary);
            font-size: 1.5rem;
        }

        .workshop-details {
            margin: 20px 0;
        }

        .workshop-details span {
            display: block;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: var(--gray);
        }

        .workshop-details i {
            margin-right: 8px;
            color: var(--primary);
        }

        .workshops-cta {
            text-align: center;
            margin-top: 60px;
            padding: 40px;
            background: var(--light);
            border-radius: 15px;
        }

        .btn-small {
            padding: 8px 20px;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .hero-stats {
                justify-content: center;
            }

            .impact-grid,
            .achievements-grid,
            .workshops-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="Email ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn" aria-pressed="false" data-lang="en" data-href="../en/index.html">EN</button>
                    <button class="lang-btn active" aria-pressed="true" data-lang="nl" data-href="index.html">NL</button>
                    <button class="lang-btn" aria-pressed="false" data-lang="pt" data-href="../pt/index.html">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <nav aria-label="Main navigation">
                    <div class="logo-area">
                        <a href="index.html" class="logo-link">
                            <div class="logo" aria-label="Logo">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <div class="logo-text">ICT<span>Tilburg</span></div>
                        </a>
                    </div>

                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>

                    <ul class="nav-links">
                        <li><a href="index.html" class="nav-item">Home</a></li>
                        <li><a href="over-ons.html" class="nav-item">Over Ons</a></li>
                        <li><a href="diensten.html" class="nav-item">Diensten</a></li>
                        <li><a href="workshops.html" class="nav-item">Workshops</a></li>
                        <li><a href="blog.html" class="nav-item">Blog & Nieuws</a></li>
                        <li><a href="forum.html" class="nav-item">Forum</a></li>
                        <li><a href="downloads.html" class="nav-item">Downloads</a></li>
                        <li><a href="partners.html" class="nav-item">Partners</a></li>
                        <li><a href="contact.html" class="nav-item">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>Technologie die Verbindt, Oplossingen die Veranderen</h1>
                    <p class="hero-subtitle">Gratis en gespecialiseerde IT-ondersteuning voor de gemeenschap van Tilburg en omgeving.</p>
                    <p class="hero-description">Voor senioren, mensen met een laag inkomen, non-profits, en iedereen die betrouwbare technische hulp zoekt door open source specialisten en innovatie voor iedereen.</p>
                    <div class="hero-buttons">
                        <a href="contact.html" class="btn btn-primary">
                            <i class="fas fa-phone"></i> Direct Hulp Nodig
                        </a>
                        <a href="diensten.html" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> Bekijk Onze Diensten
                        </a>
                    </div>
                    <div class="hero-stats">
                        <div class="stat">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Geholpen Mensen</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Gratis Service</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">Online Support</div>
                        </div>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="ICT Tilburg - Technologie voor iedereen" loading="lazy">
                </div>
            </div>
        </div>
    </section>

    <!-- Community Impact Section -->
    <section class="section community-impact">
        <div class="container">
            <div class="impact-grid">
                <div class="impact-item">
                    <div class="impact-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Voor Senioren</h3>
                    <p>Geduldig en begripvol - wij helpen ouderen de digitale wereld te ontdekken zonder stress.</p>
                </div>
                <div class="impact-item">
                    <div class="impact-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3>Voor Non-Profits</h3>
                    <p>Gratis technische ondersteuning zodat goede doelen zich kunnen richten op hun missie.</p>
                </div>
                <div class="impact-item">
                    <div class="impact-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>Voor Studenten</h3>
                    <p>Leer open source technologieën en bouw vaardigheden voor de toekomst.</p>
                </div>
                <div class="impact-item">
                    <div class="impact-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <h3>Voor Kleine Bedrijven</h3>
                    <p>Betaalbare IT-oplossingen die uw bedrijf laten groeien zonder grote investeringen.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="section services-section" id="services">
        <div class="container">
            <div class="section-title">
                <h2>Onze Diensten</h2>
                <p>Wij lossen elk IT-probleem op met empathie, deskundigheid en creativiteit. Technische ondersteuning, ontwikkeling, advies en training — altijd op maat.</p>
            </div>
            <div class="services-grid">
                <div class="service-card featured">
                    <div class="service-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="service-content">
                        <h3>Multichannel Remote Support</h3>
                        <p>E-mail, chat, WhatsApp of telefoon — altijd bereikbaar, waar u ook bent.</p>
                        <div class="service-features">
                            <span class="feature-tag">24/7 Beschikbaar</span>
                            <span class="feature-tag">Gratis</span>
                        </div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fab fa-linux"></i>
                    </div>
                    <div class="service-content">
                        <h3>Open Source Experts</h3>
                        <p>Installatie, configuratie en training in Linux, BSD en vrije software voor gebruikers en bedrijven.</p>
                        <div class="service-features">
                            <span class="feature-tag">Linux</span>
                            <span class="feature-tag">BSD</span>
                        </div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="service-content">
                        <h3>Commerciële & Industriële Beheer</h3>
                        <p>Administratieve, commerciële en industriële beheersoplossingen aangepast aan uw situatie.</p>
                        <div class="service-features">
                            <span class="feature-tag">Op Maat</span>
                            <span class="feature-tag">Professioneel</span>
                        </div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="service-content">
                        <h3>Kunstmatige Intelligentie & Automatisering</h3>
                        <p>Ontwikkeling van op maat gemaakte AI-toepassingen voor efficiëntere bedrijven.</p>
                        <div class="service-features">
                            <span class="feature-tag">AI</span>
                            <span class="feature-tag">Automatisering</span>
                        </div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="service-content">
                        <h3>Moderne Websites</h3>
                        <p>Ontwikkeling van toegankelijke websites en webshops met resultaat.</p>
                        <div class="service-features">
                            <span class="feature-tag">Responsive</span>
                            <span class="feature-tag">SEO</span>
                        </div>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="service-content">
                        <h3>Workshops & Training</h3>
                        <p>Cursussen over technologie, digitale veiligheid, open source en meer voor elk niveau.</p>
                        <div class="service-features">
                            <span class="feature-tag">Gratis</span>
                            <span class="feature-tag">Alle Niveaus</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="services-cta">
                <h3>Niet zeker welke service u nodig heeft?</h3>
                <p>Geen probleem! Neem contact met ons op en we helpen u de perfecte oplossing te vinden.</p>
                <a href="contact.html" class="btn btn-primary">
                    <i class="fas fa-comments"></i> Gratis Consultatie
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section about" id="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>Digitale Inclusie, Innovatie en Gemeenschap</h2>
                    <p>ICT Tilburg is opgericht om digitale barrières te doorbreken en technologie toegankelijk te maken. Bij ons vinden senioren, kleine bedrijven en non-profits professionele oplossingen, zonder kosten en met hart voor de mens.</p>
                    <div class="highlight">
                        <p><strong>Missie:</strong> Zeker zijn dat niemand wordt buitengesloten in het digitale tijdperk. Wij stimuleren zelfstandigheid, veiligheid en innovatie door vrije software en gepassioneerde specialisten.</p>
                    </div>
                    <p>Naast het oplossen van technische problemen helpen we onze gebruikers om zelfverzekerd technologie te gebruiken en de digitale wereld te ontdekken.</p>
                    <h3>Hoe Werkt Het?</h3>
                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <h4>Neem contact op</h4>
                            <p>Vertel ons uw behoefte via het kanaal van uw voorkeur.</p>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <h4>Persoonlijke Analyse</h4>
                            <p>Ontvang advies en diagnose van mensen die uw wereld begrijpen.</p>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <h4>Oplossing & Training</h4>
                            <p>We lossen het op én leren u zelfstandig omgaan met technologie.</p>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" alt="ICT Tilburg team">
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Achievements Section -->
    <section class="section achievements">
        <div class="container">
            <div class="section-title">
                <h2>Recente Prestaties</h2>
                <p>Samen maken we Tilburg digitaal toegankelijker</p>
            </div>
            <div class="achievements-grid">
                <div class="achievement-card">
                    <div class="achievement-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <div class="achievement-content">
                        <h3>500+ Mensen Geholpen</h3>
                        <p>In 2024 hebben we meer dan 500 mensen geholpen met hun digitale uitdagingen.</p>
                        <span class="achievement-date">2024</span>
                    </div>
                </div>
                <div class="achievement-card">
                    <div class="achievement-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="achievement-content">
                        <h3>50+ Workshops Gegeven</h3>
                        <p>Van digitale veiligheid tot Linux - onze workshops bereiken alle leeftijden.</p>
                        <span class="achievement-date">2024</span>
                    </div>
                </div>
                <div class="achievement-card">
                    <div class="achievement-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <div class="achievement-content">
                        <h3>Partnerships met Lokale Organisaties</h3>
                        <p>Samenwerking met bibliotheken, scholen en gemeenschap voor bredere impact.</p>
                        <span class="achievement-date">Lopend</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Workshops Section -->
    <section class="section workshops-section" id="workshops">
        <div class="container">
            <div class="section-title">
                <h2>Workshops & Evenementen</h2>
                <p>Doe mee met onze gratis evenementen: online veiligheid, Linux, automatisering, AI in het dagelijks leven en meer. Leer, deel ervaringen en groei samen!</p>
            </div>
            <div class="workshops-content">
                <div class="workshops-grid">
                    <div class="workshop-card upcoming">
                        <div class="workshop-badge">Aankomend</div>
                        <div class="workshop-icon"><i class="fas fa-shield-alt"></i></div>
                        <div class="workshop-content">
                            <h3>Digitale Veiligheid voor Senioren</h3>
                            <p>Bescherm uzelf tegen oplichting en bedreigingen op internet. Praktisch en voor alle leeftijden.</p>
                            <div class="workshop-details">
                                <span class="workshop-date"><i class="fas fa-calendar"></i> 28 Januari 2025</span>
                                <span class="workshop-time"><i class="fas fa-clock"></i> 14:00 - 16:00</span>
                                <span class="workshop-location"><i class="fas fa-map-marker-alt"></i> Wijkcentrum Tilburg</span>
                            </div>
                            <a href="workshops.html" class="btn btn-small">Aanmelden</a>
                        </div>
                    </div>
                    <div class="workshop-card">
                        <div class="workshop-icon"><i class="fab fa-linux"></i></div>
                        <div class="workshop-content">
                            <h3>Linux voor Beginners</h3>
                            <p>Ontdek de wereld van open source en bespaar op licenties. Installeer en gebruik Linux zonder stress.</p>
                            <div class="workshop-details">
                                <span class="workshop-date"><i class="fas fa-calendar"></i> 4 Februari 2025</span>
                                <span class="workshop-time"><i class="fas fa-clock"></i> 19:00 - 21:00</span>
                                <span class="workshop-location"><i class="fas fa-map-marker-alt"></i> Online</span>
                            </div>
                            <a href="workshops.html" class="btn btn-small">Meer Info</a>
                        </div>
                    </div>
                    <div class="workshop-card">
                        <div class="workshop-icon"><i class="fas fa-robot"></i></div>
                        <div class="workshop-content">
                            <h3>AI in het Dagelijks Leven</h3>
                            <p>Praktische AI-tools die uw leven gemakkelijker maken. Van ChatGPT tot automatisering.</p>
                            <div class="workshop-details">
                                <span class="workshop-date"><i class="fas fa-calendar"></i> 11 Februari 2025</span>
                                <span class="workshop-time"><i class="fas fa-clock"></i> 18:30 - 20:30</span>
                                <span class="workshop-location"><i class="fas fa-map-marker-alt"></i> Bibliotheek Tilburg</span>
                            </div>
                            <a href="workshops.html" class="btn btn-small">Meer Info</a>
                        </div>
                    </div>
                </div>
                <div class="workshops-cta">
                    <h3>Wilt u op de hoogte blijven van nieuwe workshops?</h3>
                    <p>Meld u aan voor onze nieuwsbrief en mis geen enkele gratis workshop meer!</p>
                    <a href="workshops.html" class="btn btn-primary">
                        <i class="fas fa-calendar-plus"></i> Alle Workshops Bekijken
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="section testimonials">
        <div class="container">
            <div class="section-title">
                <h2>Ervaringen uit de Gemeenschap</h2>
                <p>Echte verhalen van digitale transformatie in Tilburg.</p>
            </div>
            <div class="testimonial-grid">
                <div class="testimonial">
                    <p>"Ik had nooit gedacht dat ik na mijn 70e nog met computers zou leren werken. ICT Tilburg heeft me met veel geduld geholpen en nu doe ik veilig online aankopen."</p>
                    <div class="client">
                        <div class="client-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="client-info">
                            <h4>Maria Lopes</h4>
                            <p>Pensioen, Tilburg</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p>"We hebben ons kleine bedrijf kunnen digitaliseren zonder veel geld uit te geven. Nu hebben we controle over de verkoop en voorraad met open source software."</p>
                    <div class="client">
                        <div class="client-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="client-info">
                            <h4>Sebastiaan Jansen</h4>
                            <p>Ondernemer, Goirle</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <p>"De technologie-workshops hebben me niet alleen geleerd, maar ook nieuwe vrienden en vertrouwen gegeven om digitaal te zijn."</p>
                    <div class="client">
                        <div class="client-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="client-info">
                            <h4>Sandra van Dijk</h4>
                            <p>Vrijwilliger, Berkel-Enschot</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="section contact" id="contact">
        <div class="container">
            <div class="section-title">
                <h2>Neem Contact Op</h2>
                <p>Een vraag, uitdaging of idee? Wij helpen u graag bij het vinden van de perfecte technologische oplossing!</p>
            </div>
            <div class="contact-grid">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h3>Locatie</h3>
                            <p>Wijkcentrum Tilburg<br>Heuvelring 122, 5038 CL Tilburg</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div>
                            <h3>Telefoon</h3>
                            <p>+31 6 1234 5678<br>(Maandag t/m Vrijdag, 9-17u)</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <h3>Email</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <h3>Openingstijden</h3>
                            <p>Maandag t/m Vrijdag: 9:00 - 17:00<br>Zaterdag: 10:00 - 14:00</p>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form>
                        <div class="form-group">
                            <label for="name">Naam</label>
                            <input type="text" id="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="email">E-mail</label>
                            <input type="email" id="email" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Telefoon</label>
                            <input type="tel" id="phone" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="subject">Onderwerp</label>
                            <select id="subject" class="form-control" required>
                                <option value="">Kies een reden voor contact</option>
                                <option value="support">Technische Ondersteuning</option>
                                <option value="workshop">Workshops & Training</option>
                                <option value="business">Oplossingen voor Bedrijven</option>
                                <option value="website">Website Ontwikkeling</option>
                                <option value="other">Anders</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="message">Bericht</label>
                            <textarea id="message" class="form-control" required></textarea>
                        </div>
                        <button type="submit" class="btn">Bericht Versturen</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Gratis, innovatieve IT-ondersteuning in Tilburg en omgeving.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Snel naar</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="over-ons.html">Over Ons</a></li>
                        <li><a href="diensten.html">Diensten</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog & Nieuws</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Onze Diensten</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="bedrijfsbeheer.html">Bedrijfsbeheer</a></li>
                        <li><a href="ai-automatisering.html">AI & Automatisering</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nieuwsbrief</h3>
                    <p>Meld je aan voor nieuws, tips en uitnodigingen voor gratis workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Jouw e-mail" required>
                        </div>
                        <button type="submit" class="btn">Aanmelden</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>&copy; 2025 ICT Tilburg. Alle rechten voorbehouden. | Technologie voor iedereen, gemeenschap op één.</p>
                </div>
                <div class="footer-legal">
                    <a href="privacy.html">Privacybeleid</a>
                    <a href="terms-diensten.html">Servicevoorwaarden</a>
                    <a href="terms-gebruik.html">Gebruiksvoorwaarden</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Enhanced Homepage functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Header scroll effect
            const header = document.querySelector('header');
            let lastScrollY = window.scrollY;

            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }

                // Hide/show header on scroll
                if (window.scrollY > lastScrollY && window.scrollY > 200) {
                    header.style.transform = 'translateY(-100%)';
                } else {
                    header.style.transform = 'translateY(0)';
                }
                lastScrollY = window.scrollY;
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const headerHeight = header.offsetHeight;
                        const targetPosition = target.offsetTop - headerHeight - 20;

                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Animate elements on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animated');

                        // Add staggered animation for grid items
                        if (entry.target.classList.contains('impact-item') ||
                            entry.target.classList.contains('service-card') ||
                            entry.target.classList.contains('workshop-card') ||
                            entry.target.classList.contains('testimonial')) {

                            const siblings = Array.from(entry.target.parentNode.children);
                            const index = siblings.indexOf(entry.target);
                            entry.target.style.animationDelay = `${index * 0.1}s`;
                        }
                    }
                });
            }, observerOptions);

            // Observe all animatable elements
            document.querySelectorAll('.animate-on-scroll, .impact-item, .service-card, .workshop-card, .testimonial, .achievement-card, .step').forEach(el => {
                el.classList.add('animate-on-scroll');
                observer.observe(el);
            });

            // Counter animation for stats
            const animateCounters = () => {
                const counters = document.querySelectorAll('.stat-number');
                counters.forEach(counter => {
                    const target = parseInt(counter.textContent.replace(/\D/g, ''));
                    const increment = target / 100;
                    let current = 0;

                    const updateCounter = () => {
                        if (current < target) {
                            current += increment;
                            if (counter.textContent.includes('+')) {
                                counter.textContent = Math.ceil(current) + '+';
                            } else if (counter.textContent.includes('%')) {
                                counter.textContent = Math.ceil(current) + '%';
                            } else if (counter.textContent.includes('/')) {
                                counter.textContent = Math.ceil(current) + '/7';
                            } else {
                                counter.textContent = Math.ceil(current);
                            }
                            requestAnimationFrame(updateCounter);
                        } else {
                            counter.textContent = counter.textContent; // Reset to original
                        }
                    };
                    updateCounter();
                });
            };

            // Trigger counter animation when stats section is visible
            const statsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounters();
                        statsObserver.unobserve(entry.target);
                    }
                });
            });

            const heroStats = document.querySelector('.hero-stats');
            if (heroStats) {
                statsObserver.observe(heroStats);
            }

            // Form handling
            const contactForm = document.querySelector('.contact-form form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get form data
                    const formData = new FormData(this);
                    const data = Object.fromEntries(formData);

                    // Simple validation
                    const requiredFields = ['name', 'email', 'subject', 'message'];
                    let isValid = true;

                    requiredFields.forEach(field => {
                        const input = document.getElementById(field);
                        if (!input.value.trim()) {
                            input.style.borderColor = '#ef4444';
                            isValid = false;
                        } else {
                            input.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                        }
                    });

                    if (isValid) {
                        // Show success message
                        const submitBtn = this.querySelector('button[type="submit"]');
                        const originalText = submitBtn.textContent;
                        submitBtn.textContent = 'Bericht Verzonden!';
                        submitBtn.style.background = 'var(--secondary)';
                        submitBtn.disabled = true;

                        // Reset form after delay
                        setTimeout(() => {
                            this.reset();
                            submitBtn.textContent = originalText;
                            submitBtn.style.background = 'var(--gradient-accent)';
                            submitBtn.disabled = false;
                        }, 3000);
                    } else {
                        // Show error message
                        alert('Vul alle verplichte velden in.');
                    }
                });
            }

            // Newsletter form handling
            const newsletterForms = document.querySelectorAll('footer form');
            newsletterForms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const email = this.querySelector('input[type="email"]').value;
                    if (email) {
                        const btn = this.querySelector('button');
                        const originalText = btn.textContent;
                        btn.textContent = 'Aangemeld!';
                        btn.style.background = 'var(--secondary)';

                        setTimeout(() => {
                            btn.textContent = originalText;
                            btn.style.background = '';
                            this.reset();
                        }, 2000);
                    }
                });
            });

            // Mobile menu functionality
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const navLinks = document.querySelector('.nav-links');

            if (mobileMenuBtn && navLinks) {
                mobileMenuBtn.addEventListener('click', function() {
                    const isExpanded = this.getAttribute('aria-expanded') === 'true';
                    this.setAttribute('aria-expanded', !isExpanded);
                    navLinks.classList.toggle('active');

                    // Change icon
                    const icon = this.querySelector('i');
                    if (navLinks.classList.contains('active')) {
                        icon.className = 'fas fa-times';
                    } else {
                        icon.className = 'fas fa-bars';
                    }
                });

                // Close mobile menu when clicking on a link
                navLinks.querySelectorAll('a').forEach(link => {
                    link.addEventListener('click', () => {
                        navLinks.classList.remove('active');
                        mobileMenuBtn.setAttribute('aria-expanded', 'false');
                        mobileMenuBtn.querySelector('i').className = 'fas fa-bars';
                    });
                });
            }

            // Language switcher functionality
            const langButtons = document.querySelectorAll('.lang-btn');
            langButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const href = this.dataset.href;
                    if (href) {
                        window.location.href = href;
                    }
                });
            });

            // Add loading animation
            window.addEventListener('load', function() {
                document.body.classList.add('loaded');

                // Trigger hero animations
                const heroElements = document.querySelectorAll('.hero-text, .hero-image');
                heroElements.forEach((el, index) => {
                    setTimeout(() => {
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, index * 200);
                });
            });

            // Parallax effect for hero background
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const hero = document.querySelector('.hero');
                if (hero) {
                    const rate = scrolled * -0.5;
                    hero.style.transform = `translateY(${rate}px)`;
                }
            });

            // Add hover effects to cards
            const cards = document.querySelectorAll('.impact-item, .service-card, .workshop-card, .testimonial, .achievement-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>