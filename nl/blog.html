<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog & Nieuws - ICT Tilburg | Community Verhalen & Tips</title>
    <meta name="description" content="<PERSON><PERSON>rhale<PERSON>, tips en ervaringen van de ICT Tilburg community. Deel je eigen verhaal en ontdek hoe technologie levens verandert in Tilburg.">
    <meta name="keywords" content="ICT Tilburg blog, community verhalen, tech tips, digitale inclusie, senioren technologie, workshops ervaringen">
    <meta name="author" content="ICT Tilburg">
    <meta property="og:title" content="Blog & Nieuws - ICT Tilburg">
    <meta property="og:description" content="Community verhalen, tips en ervaringen over digitale inclusie in Tilburg.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://icttilburg.nl/nl/blog.html">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Blog & Nieuws - ICT Tilburg">
    <meta name="twitter:description" content="Verhalen en tips van onze community over digitale inclusie en technologie.">
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/footer-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2c5e92;
            --secondary: #43b380;
            --accent: #f39c12;
            --dark: #343a40;
            --gray: #6c757d;
            --font-main: 'Open Sans', sans-serif;
            --font-heading: 'Roboto', sans-serif;
        }
        body { font-family: var(--font-main); color: var(--dark); line-height: 1.6; background-color: #f0f4f8; }
        .container { width: 90%; max-width: 1200px; margin: 0 auto; }
        header, footer { background-color: white; }
        .header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
        .header-top .container { display: flex; justify-content: space-between; align-items: center; }
        .contact-info { display: flex; gap: 20px; }
        .contact-info a { color: white; text-decoration: none; }
        .language-selector { display: flex; gap: 10px; }
        .lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
        .lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
        .main-header { padding: 15px 0; }
        .logo-area { display: flex; align-items: center; gap: 15px; }
        .logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
        .logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
        .logo-text span { color: var(--secondary); }
        nav ul { display: flex; list-style: none; gap: 25px; }
        nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
        nav a:hover { color: var(--primary); }
        nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
        nav a:hover::after { width: 100%; }
        .mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
        .blog-hero {
            background: linear-gradient(rgba(44, 94, 146, 0.80), rgba(44, 94, 146, 0.85)), url('https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=1200&q=80') center/cover no-repeat;
            color: white;
            padding: 70px 0 50px 0;
            text-align: center;
            margin-bottom: 30px;
        }
        .blog-hero h1 { font-size: 2.4rem; margin-bottom: 14px; }
        .blog-hero p { font-size: 1.3rem; max-width: 700px; margin: 0 auto; }
        .blog-hero .hero-img {
            width: 110px;
            height: 110px;
            border-radius: 18px;
            margin: 24px auto 0 auto;
            box-shadow: 0 8px 32px rgba(44,94,146,0.10);
            border: 3px solid var(--secondary);
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .blog-hero .hero-img img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 10px;
        }
        /* Enhanced Blog Styles */
        .section { padding: 80px 0; }
        .section-title { text-align: center; margin-bottom: 50px; }
        .section-title h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
        .section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
        .section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; font-size: 1.1rem; }

        /* Featured Articles */
        .featured-articles { background: #f8f9fa; }
        .featured-grid { display: grid; grid-template-columns: 2fr 1fr; gap: 40px; margin-top: 50px; }
        .main-feature { grid-row: span 3; }
        .featured-sidebar { display: flex; flex-direction: column; gap: 30px; }

        .featured-article {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .featured-article:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .article-image {
            position: relative;
            overflow: hidden;
        }

        .main-feature .article-image {
            height: 300px;
        }

        .featured-sidebar .article-image {
            height: 150px;
        }

        .article-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .featured-article:hover .article-image img {
            transform: scale(1.05);
        }

        .article-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--accent);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .article-content {
            padding: 25px;
        }

        .featured-sidebar .article-content {
            padding: 20px;
        }

        .article-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .article-category {
            color: var(--primary);
            font-weight: 600;
        }

        .article-date {
            color: var(--gray);
        }

        .featured-article h3 {
            color: var(--dark);
            margin-bottom: 15px;
            font-size: 1.4rem;
            line-height: 1.3;
        }

        .featured-article h4 {
            color: var(--dark);
            margin-bottom: 10px;
            font-size: 1.1rem;
            line-height: 1.3;
        }

        .featured-article p {
            color: var(--gray);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .article-stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            font-size: 0.9rem;
            color: var(--gray);
        }

        .read-more-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .read-more-btn:hover {
            color: var(--secondary);
        }

        /* Blog Categories */
        .blog-categories { padding: 80px 0; }
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .category-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .category-icon {
            width: 80px;
            height: 80px;
            background: var(--light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--primary);
            font-size: 2rem;
        }

        .category-card h3 {
            color: var(--dark);
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .category-card p {
            color: var(--gray);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .category-stats {
            color: var(--gray);
            font-size: 0.9rem;
            margin-bottom: 20px;
        }

        .category-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .category-link:hover {
            color: var(--secondary);
        }

        /* Interactive Blog Section */
        .interactive-blog { background: #f8f9fa; }
        .blog-main { display: grid; grid-template-columns: 2fr 1fr; gap: 50px; margin-top: 50px; }

        @media (max-width: 1000px) {
            .blog-main { grid-template-columns: 1fr; }
            .featured-grid { grid-template-columns: 1fr; }
            .featured-sidebar { flex-direction: row; overflow-x: auto; }
        }
        /* Enhanced Blog Write Area */
        .blog-write-area {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            padding: 40px;
            margin-bottom: 40px;
        }

        .blog-write-area h3 {
            color: var(--primary);
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .write-intro {
            color: var(--gray);
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .enhanced-blog-form .form-row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .enhanced-blog-form .form-group {
            margin-bottom: 20px;
        }

        .enhanced-blog-form label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 8px;
        }

        .enhanced-blog-form label i {
            color: var(--primary);
        }

        .enhanced-blog-form input,
        .enhanced-blog-form textarea,
        .enhanced-blog-form select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            font-family: var(--font-main);
            transition: border-color 0.3s ease;
        }

        .enhanced-blog-form input:focus,
        .enhanced-blog-form textarea:focus,
        .enhanced-blog-form select:focus {
            outline: none;
            border-color: var(--primary);
        }

        .enhanced-blog-form textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .submit-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .submit-btn:hover {
            background: var(--secondary);
            transform: translateY(-2px);
        }

        .reset-btn {
            background: var(--gray);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .reset-btn:hover {
            background: var(--dark);
        }

        /* Blog Filters */
        .blog-filters {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .blog-filters h3 {
            color: var(--primary);
            margin-bottom: 20px;
        }

        .filter-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .search-box {
            position: relative;
            flex: 1;
            max-width: 300px;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
        }

        .search-box input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 2px solid var(--primary);
            background: transparent;
            color: var(--primary);
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--primary);
            color: white;
        }
        /* Enhanced Blog Articles */
        .blog-articles-list { margin-bottom: 35px; }
        .blog-article {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            padding: 30px;
            margin-bottom: 30px;
            position: relative;
            border-left: 5px solid var(--secondary);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .blog-article:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .blog-article h3 {
            color: var(--primary);
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .blog-article .blog-date {
            color: var(--gray);
            font-size: 0.9rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .blog-article .blog-content {
            white-space: pre-wrap;
            margin-bottom: 0;
            line-height: 1.6;
            color: var(--dark);
        }

        /* Blog Sidebar */
        .blog-sidebar {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .sidebar-widget {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .sidebar-widget h4 {
            color: var(--primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2rem;
        }

        /* Blog Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: var(--light);
            border-radius: 10px;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary);
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--gray);
            margin-top: 5px;
        }

        /* History List */
        .history-list {
            list-style: none;
            padding: 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .history-list li {
            margin-bottom: 15px;
            padding: 10px;
            background: var(--light);
            border-radius: 8px;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .history-list li:hover {
            background: #e3f2fd;
        }

        .history-list time {
            color: var(--primary);
            font-weight: 600;
            margin-right: 10px;
        }

        /* Popular Tags */
        .tags-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tag {
            background: var(--light);
            color: var(--primary);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tag:hover {
            background: var(--primary);
            color: white;
        }

        /* Newsletter Signup */
        .newsletter-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .newsletter-form input {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .newsletter-form input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .newsletter-form button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .newsletter-form button:hover {
            background: var(--secondary);
        }

        /* Community Highlights */
        .community-highlights {
            background: var(--primary);
            color: white;
        }

        .community-highlights .section-title h2,
        .community-highlights .section-title p {
            color: white;
        }

        .highlights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .highlight-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }

        .highlight-card:hover {
            transform: translateY(-5px);
        }

        .highlight-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .highlight-content h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .highlight-content p {
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .highlight-date {
            color: var(--secondary);
            font-weight: 600;
            font-size: 0.9rem;
        }
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .featured-grid {
                grid-template-columns: 1fr;
            }

            .featured-sidebar {
                flex-direction: column;
            }

            .categories-grid {
                grid-template-columns: 1fr;
            }

            .blog-main {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .enhanced-blog-form .form-row {
                grid-template-columns: 1fr;
            }

            .filter-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                max-width: none;
            }

            .filter-buttons {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .highlights-grid {
                grid-template-columns: 1fr;
            }

            .section-title h2 {
                font-size: 1.8rem;
            }

            .blog-hero h1 {
                font-size: 2rem;
            }

            .blog-hero p {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .section {
                padding: 60px 0;
            }

            .blog-write-area,
            .sidebar-widget {
                padding: 20px;
            }

            .form-actions {
                flex-direction: column;
            }

            .submit-btn,
            .reset-btn {
                justify-content: center;
            }
        }
    </style>
    <script src="../assets/js/common.js"></script>
    <link rel="alternate" hreflang="en" href="../en/blog.html">
    <link rel="alternate" hreflang="pt" href="../pt/blog.html">
    <link rel="canonical" href="blog.html">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="contact-info" aria-label="Contact information">
                    <a href="mailto:<EMAIL>" aria-label="Email ICT Tilburg"><i class="fas fa-envelope"></i> <EMAIL></a>
                    <a href="tel:+31612345678" aria-label="Call ICT Tilburg"><i class="fas fa-phone"></i> +31 6 1234 5678</a>
                </div>
                <div class="language-selector" role="navigation" aria-label="Language selection">
                    <button class="lang-btn" aria-pressed="false" data-lang="en" data-href="../en/blog.html">EN</button>
                    <button class="lang-btn active" aria-pressed="true" data-lang="nl" data-href="blog.html">NL</button>
                    <button class="lang-btn" aria-pressed="false" data-lang="pt" data-href="../pt/blog.html">PT</button>
                </div>
            </div>
        </div>
        <div class="main-header">
            <div class="container">
                <nav aria-label="Main navigation">
                    <div class="logo-area">
                        <a href="index.html" class="logo-link">
                            <div class="logo" aria-label="Logo">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <div class="logo-text">ICT<span>Tilburg</span></div>
                        </a>
                    </div>

                    <button class="mobile-menu-btn" aria-label="Open menu" aria-expanded="false">
                        <i class="fas fa-bars"></i>
                    </button>

                    <ul class="nav-links">
                        <li><a href="index.html" class="nav-item">Home</a></li>
                        <li><a href="over-ons.html" class="nav-item">Over Ons</a></li>
                        <li><a href="diensten.html" class="nav-item">Diensten</a></li>
                        <li><a href="workshops.html" class="nav-item">Workshops</a></li>
                        <li><a href="blog.html" class="nav-item">Blog & Nieuws</a></li>
                        <li><a href="forum.html" class="nav-item">Forum</a></li>
                        <li><a href="downloads.html" class="nav-item">Downloads</a></li>
                        <li><a href="partners.html" class="nav-item">Partners</a></li>
                        <li><a href="contact.html" class="nav-item">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Blog Hero -->
    <section class="blog-hero">
        <div class="container">
            <div class="hero-img">
                <img src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=400&q=80" alt="Tech team">
            </div>
            <h1>ICT Tilburg Blog</h1>
            <p>Verhalen, ervaringen en het dagelijks leven bij ICT Tilburg. Lees hoe wij technologie inzetten voor de gemeenschap, ontdek tips en laat je inspireren door onze belevenissen!</p>
        </div>
    </section>

    <main>
    <!-- Featured Articles Section -->
    <section class="section featured-articles">
        <div class="container">
            <div class="section-title">
                <h2>Uitgelichte Artikelen</h2>
                <p>Ontdek onze meest populaire verhalen en tips uit de ICT Tilburg gemeenschap</p>
            </div>
            <div class="featured-grid">
                <article class="featured-article main-feature">
                    <div class="article-image">
                        <img src="https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Digitale inclusie workshop" loading="lazy">
                        <div class="article-badge">Populair</div>
                    </div>
                    <div class="article-content">
                        <div class="article-meta">
                            <span class="article-category"><i class="fas fa-tag"></i> Workshops</span>
                            <span class="article-date"><i class="fas fa-calendar"></i> 15 december 2024</span>
                        </div>
                        <h3>Digitale Inclusie: Hoe We 200+ Senioren Hielpen</h3>
                        <p>Een kijkje achter de schermen van onze succesvolle workshop serie voor senioren. Lees hoe we samen de digitale kloof overbruggen en wat we hebben geleerd van onze deelnemers.</p>
                        <div class="article-stats">
                            <span><i class="fas fa-eye"></i> 1.2k views</span>
                            <span><i class="fas fa-heart"></i> 89 likes</span>
                            <span><i class="fas fa-comment"></i> 23 reacties</span>
                        </div>
                        <a href="#" class="read-more-btn">Lees verder <i class="fas fa-arrow-right"></i></a>
                    </div>
                </article>

                <div class="featured-sidebar">
                    <article class="featured-article">
                        <div class="article-image">
                            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Remote support" loading="lazy">
                        </div>
                        <div class="article-content">
                            <div class="article-meta">
                                <span class="article-category"><i class="fas fa-tag"></i> Tips</span>
                                <span class="article-date"><i class="fas fa-calendar"></i> 12 december 2024</span>
                            </div>
                            <h4>5 Tips voor Veilig Online Bankieren</h4>
                            <p>Praktische tips om veilig te bankieren via internet, speciaal voor onze senioren.</p>
                            <a href="#" class="read-more-btn">Lees verder <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>

                    <article class="featured-article">
                        <div class="article-image">
                            <img src="https://images.unsplash.com/photo-*************-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Community event" loading="lazy">
                        </div>
                        <div class="article-content">
                            <div class="article-meta">
                                <span class="article-category"><i class="fas fa-tag"></i> Evenementen</span>
                                <span class="article-date"><i class="fas fa-calendar"></i> 10 december 2024</span>
                            </div>
                            <h4>Terugblik: Tech Café December</h4>
                            <p>Een gezellige middag vol technische vragen, koffie en nieuwe vriendschappen.</p>
                            <a href="#" class="read-more-btn">Lees verder <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>

                    <article class="featured-article">
                        <div class="article-image">
                            <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Smartphone tips" loading="lazy">
                        </div>
                        <div class="article-content">
                            <div class="article-meta">
                                <span class="article-category"><i class="fas fa-tag"></i> Tutorials</span>
                                <span class="article-date"><i class="fas fa-calendar"></i> 8 december 2024</span>
                            </div>
                            <h4>WhatsApp voor Beginners</h4>
                            <p>Stap-voor-stap uitleg hoe je WhatsApp installeert en gebruikt om contact te houden.</p>
                            <a href="#" class="read-more-btn">Lees verder <i class="fas fa-arrow-right"></i></a>
                        </div>
                    </article>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Categories Section -->
    <section class="section blog-categories">
        <div class="container">
            <div class="section-title">
                <h2>Ontdek per Categorie</h2>
                <p>Vind precies wat je zoekt in onze georganiseerde categorieën</p>
            </div>
            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="category-content">
                        <h3>Workshops & Training</h3>
                        <p>Verhalen en ervaringen van onze workshops en trainingen</p>
                        <div class="category-stats">
                            <span>24 artikelen</span>
                            <span>•</span>
                            <span>Laatste: 2 dagen geleden</span>
                        </div>
                        <a href="#" class="category-link">Bekijk artikelen <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="category-content">
                        <h3>Tips & Tricks</h3>
                        <p>Praktische tips voor dagelijks computergebruik</p>
                        <div class="category-stats">
                            <span>18 artikelen</span>
                            <span>•</span>
                            <span>Laatste: 1 dag geleden</span>
                        </div>
                        <a href="#" class="category-link">Bekijk artikelen <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="category-content">
                        <h3>Community Stories</h3>
                        <p>Verhalen van onze vrijwilligers en deelnemers</p>
                        <div class="category-stats">
                            <span>31 artikelen</span>
                            <span>•</span>
                            <span>Laatste: 3 dagen geleden</span>
                        </div>
                        <a href="#" class="category-link">Bekijk artikelen <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="category-content">
                        <h3>Evenementen</h3>
                        <p>Verslagen van onze evenementen en bijeenkomsten</p>
                        <div class="category-stats">
                            <span>15 artikelen</span>
                            <span>•</span>
                            <span>Laatste: 1 week geleden</span>
                        </div>
                        <a href="#" class="category-link">Bekijk artikelen <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="category-content">
                        <h3>Veiligheid</h3>
                        <p>Alles over online veiligheid en privacy</p>
                        <div class="category-stats">
                            <span>12 artikelen</span>
                            <span>•</span>
                            <span>Laatste: 4 dagen geleden</span>
                        </div>
                        <a href="#" class="category-link">Bekijk artikelen <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="category-content">
                        <h3>Mobiele Apparaten</h3>
                        <p>Tips voor smartphones en tablets</p>
                        <div class="category-stats">
                            <span>20 artikelen</span>
                            <span>•</span>
                            <span>Laatste: 5 dagen geleden</span>
                        </div>
                        <a href="#" class="category-link">Bekijk artikelen <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Blog Section -->
    <section class="section interactive-blog" id="blog">
        <div class="container">
            <div class="section-title">
                <h2>Deel Jouw Verhaal</h2>
                <p>Word onderdeel van onze community en deel je ervaringen met ICT Tilburg</p>
            </div>
            <div class="blog-main">
                <!-- Blog schrijven -->
                <div class="blog-content">
                    <div class="blog-write-area">
                        <h3>Schrijf een nieuw verhaal</h3>
                        <p class="write-intro">Heb je een leuke ervaring gehad met onze diensten? Een tip die anderen kan helpen? Deel het met de community!</p>
                        <form id="blogForm" class="enhanced-blog-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="blogTitle"><i class="fas fa-heading"></i> Titel van je verhaal</label>
                                    <input type="text" id="blogTitle" placeholder="Bijv. Mijn eerste videogesprek met kleinkinderen" required>
                                </div>
                                <div class="form-group">
                                    <label for="blogCategory"><i class="fas fa-tag"></i> Categorie</label>
                                    <select id="blogCategory" required>
                                        <option value="">Kies een categorie</option>
                                        <option value="workshops">Workshops & Training</option>
                                        <option value="tips">Tips & Tricks</option>
                                        <option value="community">Community Stories</option>
                                        <option value="events">Evenementen</option>
                                        <option value="security">Veiligheid</option>
                                        <option value="mobile">Mobiele Apparaten</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="blogContent"><i class="fas fa-edit"></i> Jouw verhaal</label>
                                <textarea id="blogContent" placeholder="Vertel hier je verhaal, ervaring of tip. Wat heb je geleerd? Hoe heeft ICT Tilburg je geholpen? Welke tip wil je delen?" required></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="submit-btn">
                                    <i class="fas fa-paper-plane"></i> Verhaal Publiceren
                                </button>
                                <button type="reset" class="reset-btn">
                                    <i class="fas fa-undo"></i> Opnieuw
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Filter en zoek opties -->
                    <div class="blog-filters">
                        <h3>Alle Verhalen</h3>
                        <div class="filter-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="searchInput" placeholder="Zoek in verhalen...">
                            </div>
                            <div class="filter-buttons">
                                <button class="filter-btn active" data-filter="all">Alle</button>
                                <button class="filter-btn" data-filter="workshops">Workshops</button>
                                <button class="filter-btn" data-filter="tips">Tips</button>
                                <button class="filter-btn" data-filter="community">Community</button>
                                <button class="filter-btn" data-filter="events">Evenementen</button>
                            </div>
                        </div>
                    </div>

                    <!-- Lijst met artikelen -->
                    <div id="articlesList" class="blog-articles-list"></div>
                </div>

                <!-- Blog Sidebar -->
                <aside class="blog-sidebar">
                    <div class="sidebar-widget blog-stats">
                        <h4><i class="fas fa-chart-bar"></i> Blog Statistieken</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number" id="totalArticles">0</div>
                                <div class="stat-label">Totaal Verhalen</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">1.2k</div>
                                <div class="stat-label">Lezers</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">89</div>
                                <div class="stat-label">Actieve Schrijvers</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">4.8</div>
                                <div class="stat-label">Gem. Waardering</div>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-widget blog-history">
                        <h4><i class="fas fa-history"></i> Recente Verhalen</h4>
                        <ul id="historyList" class="history-list"></ul>
                    </div>

                    <div class="sidebar-widget popular-tags">
                        <h4><i class="fas fa-tags"></i> Populaire Tags</h4>
                        <div class="tags-cloud">
                            <span class="tag">WhatsApp</span>
                            <span class="tag">Senioren</span>
                            <span class="tag">Veiligheid</span>
                            <span class="tag">Workshop</span>
                            <span class="tag">Smartphone</span>
                            <span class="tag">E-mail</span>
                            <span class="tag">Internet</span>
                            <span class="tag">Tablet</span>
                            <span class="tag">Videobellen</span>
                            <span class="tag">Online Banking</span>
                        </div>
                    </div>

                    <div class="sidebar-widget newsletter-signup">
                        <h4><i class="fas fa-envelope"></i> Blog Updates</h4>
                        <p>Ontvang een melding bij nieuwe verhalen</p>
                        <form class="newsletter-form">
                            <input type="email" placeholder="Jouw e-mail" required>
                            <button type="submit">
                                <i class="fas fa-bell"></i> Aanmelden
                            </button>
                        </form>
                    </div>
                </aside>
            </div>
        </div>
    </section>

    <!-- Community Highlights Section -->
    <section class="section community-highlights">
        <div class="container">
            <div class="section-title">
                <h2>Community Hoogtepunten</h2>
                <p>Bijzondere momenten en successen uit onze gemeenschap</p>
            </div>
            <div class="highlights-grid">
                <div class="highlight-card">
                    <div class="highlight-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="highlight-content">
                        <h3>200+ Senioren Geholpen</h3>
                        <p>Dit jaar hebben we meer dan 200 senioren geholpen met digitale vaardigheden</p>
                        <div class="highlight-date">December 2024</div>
                    </div>
                </div>

                <div class="highlight-card">
                    <div class="highlight-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="highlight-content">
                        <h3>50 Vrijwilligers Actief</h3>
                        <p>Onze geweldige community van vrijwilligers groeit elke maand</p>
                        <div class="highlight-date">November 2024</div>
                    </div>
                </div>

                <div class="highlight-card">
                    <div class="highlight-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="highlight-content">
                        <h3>4.9/5 Waardering</h3>
                        <p>Onze deelnemers geven ons gemiddeld een 4.9 uit 5 sterren</p>
                        <div class="highlight-date">Afgelopen jaar</div>
                    </div>
                </div>

                <div class="highlight-card">
                    <div class="highlight-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="highlight-content">
                        <h3>100+ Workshops</h3>
                        <p>Meer dan 100 workshops en events georganiseerd dit jaar</p>
                        <div class="highlight-date">2024 Totaal</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-area">
                        <div class="logo">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="logo-text">ICT<span>Tilburg</span></div>
                    </div>
                    <p>Gratis, innovatieve IT-ondersteuning in Tilburg en omgeving.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Snel naar</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="over-ons.html">Over Ons</a></li>
                        <li><a href="diensten.html">Diensten</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                        <li><a href="blog.html">Blog & Nieuws</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Onze Diensten</h3>
                    <ul>
                        <li><a href="remote-support.html">Remote Support</a></li>
                        <li><a href="open-source.html">Open Source</a></li>
                        <li><a href="bedrijfsbeheer.html">Bedrijfsbeheer</a></li>
                        <li><a href="ai-automatisering.html">AI & Automatisering</a></li>
                        <li><a href="workshops.html">Workshops</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Nieuwsbrief</h3>
                    <p>Meld je aan voor nieuws, tips en uitnodigingen voor gratis workshops.</p>
                    <form>
                        <div class="form-group">
                            <input type="email" class="form-control" placeholder="Jouw e-mail" required>
                        </div>
                        <button type="submit" class="btn">Aanmelden</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>&copy; 2025 ICT Tilburg. Alle rechten voorbehouden. | Technologie voor iedereen, gemeenschap op één.</p>
                </div>
                <div class="footer-legal">
                    <a href="privacy.html">Privacybeleid</a>
                    <a href="terms-diensten.html">Servicevoorwaarden</a>
                    <a href="terms-gebruik.html">Gebruiksvoorwaarden</a>
                </div>
            </div>
        </div>
    </footer>
    <script>
        // Enhanced Blog functionality
        let currentFilter = 'all';
        let searchTerm = '';

        // Blog artikelen lokaal opslaan & tonen
        function getBlogArticles() {
            return JSON.parse(localStorage.getItem('icttilburg_blog_articles') || '[]');
        }

        function saveBlogArticles(articles) {
            localStorage.setItem('icttilburg_blog_articles', JSON.stringify(articles));
        }

        function formatDateISOtoNL(iso) {
            const d = new Date(iso);
            return d.toLocaleDateString("nl-NL", { year: 'numeric', month: 'long', day: 'numeric' });
        }

        function getCategoryName(category) {
            const categories = {
                'workshops': 'Workshops & Training',
                'tips': 'Tips & Tricks',
                'community': 'Community Stories',
                'events': 'Evenementen',
                'security': 'Veiligheid',
                'mobile': 'Mobiele Apparaten'
            };
            return categories[category] || 'Algemeen';
        }

        function filterArticles(articles) {
            return articles.filter(article => {
                const matchesFilter = currentFilter === 'all' || article.category === currentFilter;
                const matchesSearch = searchTerm === '' ||
                    article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    article.content.toLowerCase().includes(searchTerm.toLowerCase());
                return matchesFilter && matchesSearch;
            });
        }

        function renderArticles() {
            const allArticles = getBlogArticles().sort((a, b) => new Date(b.date) - new Date(a.date));
            const filteredArticles = filterArticles(allArticles);
            const articlesList = document.getElementById('articlesList');

            articlesList.innerHTML = '';

            if (filteredArticles.length === 0) {
                articlesList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--gray);">
                        <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3>Geen verhalen gevonden</h3>
                        <p>Probeer een andere zoekterm of filter.</p>
                    </div>
                `;
                return;
            }

            for (const art of filteredArticles) {
                const el = document.createElement('div');
                el.className = 'blog-article';
                el.innerHTML = `
                    <h3>${art.title}</h3>
                    <div class="blog-date">
                        <i class="fas fa-calendar-day"></i> ${formatDateISOtoNL(art.date)}
                        ${art.category ? `<span style="margin-left: 15px; color: var(--primary);"><i class="fas fa-tag"></i> ${getCategoryName(art.category)}</span>` : ''}
                    </div>
                    <div class="blog-content">${art.content.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</div>
                `;
                articlesList.appendChild(el);
            }

            renderHistory(allArticles);
            updateStats(allArticles);
        }

        function renderHistory(articles) {
            const historyList = document.getElementById('historyList');
            historyList.innerHTML = '';

            const recentArticles = articles.slice(0, 10);

            if (recentArticles.length === 0) {
                historyList.innerHTML = '<li style="color: var(--gray); font-style: italic;">Nog geen verhalen geschreven</li>';
                return;
            }

            recentArticles.forEach(art => {
                const li = document.createElement('li');
                li.innerHTML = `<time>${formatDateISOtoNL(art.date)}</time> ${art.title}`;
                historyList.appendChild(li);
            });
        }

        function updateStats(articles) {
            const totalArticlesEl = document.getElementById('totalArticles');
            if (totalArticlesEl) {
                totalArticlesEl.textContent = articles.length;
            }
        }

        // Form submission
        document.getElementById('blogForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const title = document.getElementById('blogTitle').value.trim();
            const content = document.getElementById('blogContent').value.trim();
            const category = document.getElementById('blogCategory').value;

            if (title && content && category) {
                const articles = getBlogArticles();
                articles.push({
                    title,
                    content,
                    category,
                    date: new Date().toISOString()
                });
                saveBlogArticles(articles);
                renderArticles();
                this.reset();

                // Show success message
                const submitBtn = this.querySelector('.submit-btn');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-check"></i> Verhaal Toegevoegd!';
                submitBtn.style.background = 'var(--secondary)';

                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = 'var(--primary)';
                }, 2000);
            }
        });

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            searchTerm = e.target.value;
            renderArticles();
        });

        // Filter functionality
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentFilter = this.dataset.filter;
                renderArticles();
            });
        });

        // Newsletter signup
        document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                const btn = this.querySelector('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Aangemeld!';
                btn.style.background = 'var(--secondary)';

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = 'var(--primary)';
                    this.reset();
                }, 2000);
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderArticles();

            // Add some sample articles if none exist
            const articles = getBlogArticles();
            if (articles.length === 0) {
                const sampleArticles = [
                    {
                        title: "Mijn eerste WhatsApp videogesprek",
                        content: "Vandaag heb ik voor het eerst een videogesprek gevoerd met mijn kleinkinderen via WhatsApp. Dankzij de workshop van ICT Tilburg weet ik nu hoe het werkt. Het was zo fijn om hun gezichtjes weer te zien!",
                        category: "community",
                        date: new Date(Date.now() - ********).toISOString()
                    },
                    {
                        title: "Tips voor veilig online bankieren",
                        content: "Na de workshop over online veiligheid deel ik graag deze tips: gebruik altijd de officiële app van je bank, log altijd uit na gebruik, en deel nooit je inloggegevens. Simpel maar effectief!",
                        category: "security",
                        date: new Date(Date.now() - *********).toISOString()
                    },
                    {
                        title: "Geweldige workshop over tablets",
                        content: "Gisteren was ik bij de tablet workshop. Ik heb geleerd hoe ik foto's kan maken, apps kan downloaden en zelfs hoe ik kan videobellen. De vrijwilligers waren zo geduldig en vriendelijk!",
                        category: "workshops",
                        date: new Date(Date.now() - *********).toISOString()
                    }
                ];
                saveBlogArticles(sampleArticles);
                renderArticles();
            }
        });
    </script>
</body>
</html>