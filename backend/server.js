const express = require('express');
const { PrismaClient } = require('@prisma/client');
const cors = require('cors');
const app = express();
const prisma = new PrismaClient();

app.use(cors());
app.use(express.json());

// CRUD Clientes
app.get('/clientes', async (req, res) => {
  const clientes = await prisma.cliente.findMany({ include: { atividades: true } });
  res.json(clientes);
});

app.post('/clientes', async (req, res) => {
  const { nome, email, telefone } = req.body;
  const cliente = await prisma.cliente.create({ data: { nome, email, telefone } });
  res.json(cliente);
});

// CRUD Atividades
app.get('/atividades', async (req, res) => {
  const atividades = await prisma.atividade.findMany({ include: { cliente: true } });
  res.json(atividades);
});

app.post('/atividades', async (req, res) => {
  const { clienteId, tipo, descricao, data, duracao } = req.body;
  const atividade = await prisma.atividade.create({
    data: { clienteId, tipo, descricao, data: new Date(data), duracao }
  });
  res.json(atividade);
});

// Estatísticas simples (exemplo)
app.get('/estatisticas/clientes', async (req, res) => {
  const total = await prisma.cliente.count();
  res.json({ total_clientes: total });
});

// Start server
const port = process.env.PORT || 4000;
app.listen(port, () => console.log(`API running on http://localhost:${port}`));