<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICT Tilburg - Gratis IT Ondersteuning & Digitale Inclusie | Vrijwilligers voor Technologie</title>
    <meta name="description" content="ICT Tilburg biedt gratis IT ondersteuning, workshops en technische hulp voor iedereen in Tilburg. Vrijwilligers die technologie toegankelijk maken voor senioren, bedrijven en de gemeenschap.">
    <meta name="keywords" content="ICT Tilburg, gratis IT support, digitale inclusie, workshops, Linux, open source, AI, website ontwikkeling, vrijwilligers, Tilburg">
    <meta name="author" content="ICT Tilburg">
    <meta property="og:title" content="ICT Tilburg - Gratis IT Ondersteuning & Digitale Inclusie">
    <meta property="og:description" content="Vrijwilligers die technologie toegankelijk maken voor iedereen in Tilburg. Gratis IT support, workshops en innovatieve oplossingen.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://icttilburg.nl">
    <meta property="og:image" content="https://icttilburg.nl/assets/images/og-image.jpg">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="ICT Tilburg - Gratis IT Ondersteuning">
    <meta name="twitter:description" content="Vrijwilligers die technologie toegankelijk maken voor iedereen in Tilburg.">
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/footer-enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #1e40af;
            --primary-dark: #1e3a8a;
            --secondary: #10b981;
            --secondary-dark: #059669;
            --accent: #f59e0b;
            --accent-dark: #d97706;
            --light: #f8fafc;
            --light-blue: #eff6ff;
            --dark: #1f2937;
            --gray: #6b7280;
            --gray-light: #9ca3af;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary) 0%, var(--secondary-dark) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --font-main: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-main);
            color: var(--dark);
            line-height: 1.6;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            overflow-x: hidden;
        }

        /* Smooth animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: var(--transition);
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }
        .container {
            width: 90%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Enhanced Header */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        header.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .header-top {
            background: var(--gradient-primary);
            color: white;
            padding: 10px 0;
            font-size: 0.9rem;
        }

        .header-top .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .contact-info {
            display: flex;
            gap: 25px;
        }

        .contact-info a {
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
            opacity: 0.9;
        }

        .contact-info a:hover {
            opacity: 1;
            transform: translateY(-1px);
        }

        .language-selector {
            display: flex;
            gap: 5px;
        }

        .lang-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            font-size: 0.85rem;
            padding: 6px 12px;
            border-radius: 20px;
            transition: var(--transition);
            font-weight: 500;
        }

        .lang-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .lang-btn.active {
            background: white;
            color: var(--primary);
            font-weight: 600;
        }

        .main-header {
            padding: 20px 0;
            background: white;
        }

        .main-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-area {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 65px;
            height: 65px;
            background: var(--gradient-secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .logo:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-lg);
        }

        .logo-text {
            font-family: var(--font-heading);
            font-weight: 700;
            font-size: 2rem;
            color: var(--primary);
            letter-spacing: -0.5px;
        }

        .logo-text span {
            color: var(--secondary);
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 30px;
            align-items: center;
        }

        nav a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            font-size: 1rem;
            transition: var(--transition);
            position: relative;
            padding: 8px 16px;
            border-radius: var(--border-radius);
        }

        nav a:hover {
            color: var(--primary);
            background: var(--light-blue);
            transform: translateY(-1px);
        }

        nav a.active {
            color: var(--primary);
            background: var(--light-blue);
            font-weight: 600;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--primary);
            padding: 8px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .mobile-menu-btn:hover {
            background: var(--light-blue);
        }

        /* Add space for fixed header */
        body {
            padding-top: 140px;
        }
        footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
        .footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
        .footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
        .footer-col ul { list-style: none; }
        .footer-col ul li { margin-bottom: 10px; }
        .footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
        .footer-col a:hover { color: var(--secondary); }
        .social-links { display: flex; gap: 15px; margin-top: 20px; }
        .social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
        .social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
        .copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
    </style>
    <!-- SEO Meta tags -->
    <script src="assets/js/common.js"></script>
    <!-- Language alternatives -->
    <link rel="alternate" hreflang="en" href="en/index.html">
    <link rel="alternate" hreflang="pt" href="pt/index.html">
    <link rel="canonical" href="index.html">
</head>
<body>
    <script>
        // Redirect to Dutch version immediately
        window.location.replace('./nl/index.html');
    </script>
    
    <!-- Fallback content in case JavaScript is disabled -->
    <noscript>
        <meta http-equiv="refresh" content="0; url=./nl/index.html">
    </noscript>
    
    <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h1>ICT Tilburg</h1>
        <p>Redirecting to the Dutch homepage...</p>
        <p><a href="./nl/index.html">Click here if you are not redirected automatically</a></p>
    </div>
</body>
</html>
