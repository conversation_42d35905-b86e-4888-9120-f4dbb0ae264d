// Base path configuration for ICT Tilburg website
// This script handles serving the site under a subpath (e.g., /ict-tilburg)

(function() {
    // Get the base path from the URL
    function getBasePath() {
        // Check if we're running on the production server with /ict-tilburg path
        if (window.location.hostname === '**************' || 
            window.location.pathname.includes('/ict-tilburg')) {
            return '/ict-tilburg/';
        }
        // Local development
        return '/';
    }

    // Set base element for the entire site
    function setBaseElement() {
        // Remove existing base element if any
        const existingBase = document.querySelector('base');
        if (existingBase) {
            existingBase.remove();
        }

        // Add new base element
        const base = document.createElement('base');
        base.href = getBasePath();
        document.head.insertBefore(base, document.head.firstChild);
    }

    // Fix language references when on subpath
    function fixLanguageReferences() {
        // Fix the meta refresh
        const metaRefresh = document.querySelector('meta[http-equiv="refresh"]');
        if (metaRefresh) {
            const content = metaRefresh.getAttribute('content');
            if (content && content.includes('url=')) {
                // Already has ict-tilburg in the path
                if (!content.includes('/ict-tilburg/')) {
                    const newContent = content.replace('url=', 'url=/ict-tilburg/');
                    metaRefresh.setAttribute('content', newContent);
                }
            }
        }
        
        // Fix redirects in JavaScript
        window.onSubpathRedirect = function(url) {
            if (window.location.hostname === '**************' || 
                window.location.pathname.includes('/ict-tilburg')) {
                if (!url.startsWith('/ict-tilburg/')) {
                    return '/ict-tilburg' + url;
                }
            }
            return url;
        };
    }

    // Fix all redirects when the DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        setBaseElement();
        fixLanguageReferences();
        
        // Override native redirects
        const originalAssign = window.location.assign;
        window.location.assign = function(url) {
            originalAssign.call(window.location, window.onSubpathRedirect(url));
        };
        
        const originalReplace = window.location.replace;
        window.location.replace = function(url) {
            originalReplace.call(window.location, window.onSubpathRedirect(url));
        };
        
        // Store original redirect
        const originalHref = window.location.href;
        Object.defineProperty(window.location, 'href', {
            set: function(url) {
                window.location.assign(window.onSubpathRedirect(url));
            },
            get: function() {
                return originalHref;
            }
        });
    });
})();
