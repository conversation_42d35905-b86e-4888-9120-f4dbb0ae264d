document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navLinks = document.querySelector('.nav-links');

    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function() {
            navLinks.classList.toggle('active');
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-bars');
                icon.classList.toggle('fa-times');
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navLinks.contains(e.target) && !mobileMenuBtn.contains(e.target) && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                const icon = mobileMenuBtn.querySelector('i');
                if (icon) {
                    icon.classList.add('fa-bars');
                    icon.classList.remove('fa-times');
                }
            }
        });
    }

    // Set active navigation item
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        const itemPath = item.getAttribute('href');
        if (itemPath === currentPath || 
            (currentPath === '/' && itemPath === '/index.html')) {
            item.classList.add('active');
        }
    });

    // Language selector
    const langSelector = document.querySelector('.language-selector');
    if (langSelector) {
        langSelector.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', function(e) {
                const lang = this.getAttribute('data-lang');
                localStorage.setItem('preferred_language', lang);
            });
        });

        // Set active language
        const currentLang = document.documentElement.lang;
        const langLink = langSelector.querySelector(`[data-lang="${currentLang}"]`);
        if (langLink) {
            langLink.classList.add('active');
        }
    }
});
