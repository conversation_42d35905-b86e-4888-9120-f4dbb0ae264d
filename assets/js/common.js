// Common JavaScript functionality for ICT Tilburg website

document.addEventListener('DOMContentLoaded', function() {
    // Mobile Menu Toggle with ARIA
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('nav ul');
    
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', () => {
            navMenu.classList.toggle('show');
            const expanded = mobileMenuBtn.getAttribute('aria-expanded') === 'true';
            mobileMenuBtn.setAttribute('aria-expanded', (!expanded).toString());
        });
    }

    // Language switcher
    const langButtons = document.querySelectorAll('.lang-btn');
    langButtons.forEach(button => {
        button.addEventListener('click', () => {
            langButtons.forEach(btn => btn.classList.remove('active'));
            langButtons.forEach(btn => btn.setAttribute('aria-pressed', 'false'));
            button.classList.add('active');
            button.setAttribute('aria-pressed', 'true');
            
            // Store language preference
            const lang = button.getAttribute('data-lang');
            if (lang) {
                localStorage.setItem('preferred_language', lang);
            }
            
            // Navigate to the appropriate page
            const href = button.getAttribute('data-href');
            if (href) {
                window.location.href = href;
            }
        });
    });

    // Newsletter form submission
    const newsletterForms = document.querySelectorAll('.newsletter-form');
    newsletterForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                // Here you would typically send the email to your backend
                alert('Bedankt voor uw aanmelding! We houden u op de hoogte.');
                this.reset();
            }
        });
    });

    // Contact form submission
    const contactForms = document.querySelectorAll('.contact-form form');
    contactForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            // Here you would typically send the form data to your backend
            alert('Bedankt voor uw bericht! We nemen spoedig contact op.');
            this.reset();
        });
    });

    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // FAQ accordion functionality
    const faqDetails = document.querySelectorAll('.faq-list details');
    faqDetails.forEach(detail => {
        detail.addEventListener('toggle', function() {
            if (this.open) {
                // Close other open details
                faqDetails.forEach(otherDetail => {
                    if (otherDetail !== this && otherDetail.open) {
                        otherDetail.open = false;
                    }
                });
            }
        });
    });
});

// Utility function to set language preference
function setLanguagePreference(lang) {
    localStorage.setItem('preferred_language', lang);
}

// Utility function to get language preference
function getLanguagePreference() {
    return localStorage.getItem('preferred_language');
}

// Function to detect browser language
function getBrowserLanguage() {
    return (navigator.language || navigator.userLanguage || '').split('-')[0];
}
