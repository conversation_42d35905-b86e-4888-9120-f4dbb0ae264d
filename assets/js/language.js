// Enhanced language handling with improved page mapping and seamless transitions for NL directory
const language<PERSON><PERSON><PERSON> = {
    // Get the base path for the site (handles subpath like /ict-tilburg/)
    getBasePath: function() {
        // Check if we're running on the production server or with a subpath
        if (window.location.hostname === '**************' || 
            window.location.pathname.includes('/ict-tilburg')) {
            return '/ict-tilburg/';
        }
        return '';
    },

    // Language file mappings for pages that have different names in different languages
    pageMappings: {
        // Dutch to English mappings
        'over-ons.html': {
            'en': 'about-us.html',
            'pt': 'sobre-nos.html',
            'nl': 'over-ons.html'
        },
        'diensten.html': {
            'en': 'services.html',
            'pt': 'servicos.html',
            'nl': 'diensten.html'
        },
        'contact.html': {
            'en': 'contact.html',
            'pt': 'contacto.html',
            'nl': 'contact.html'
        },
        'partners.html': {
            'en': 'partners.html',
            'pt': 'parceiros.html',
            'nl': 'partners.html'
        },
        'index.html': {
            'en': 'index.html',
            'pt': 'index.html',
            'nl': 'index.html'
        },
        'privacy.html': {
            'en': 'privacy.html',
            'pt': 'politica-de-privacidade.html',
            'nl': 'privacy.html'
        },
        'terms-gebruik.html': {
            'en': 'terms-of-use.html',
            'pt': 'termos-de-uso.html',
            'nl': 'terms-gebruik.html'
        },
        'terms-diensten.html': {
            'en': 'terms-of-service.html',
            'pt': 'termos-de-servico.html',
            'nl': 'terms-diensten.html'
        },
        'workshops.html': {
            'en': 'workshops.html',
            'pt': 'workshops.html',
            'nl': 'workshops.html'
        },
        'blog.html': {
            'en': 'blog.html',
            'pt': 'blog.html',
            'nl': 'blog.html'
        },
        'nieuws.html': {
            'en': 'news.html',
            'nl': 'nieuws.html'
        },
        'downloads.html': {
            'en': 'downloads.html',
            'pt': 'downloads.html',
            'nl': 'downloads.html'
        },
        'forum.html': {
            'en': 'forum.html',
            'pt': 'forum.html',
            'nl': 'forum.html'
        },
        'about-us.html': {
            'nl': 'over-ons.html',
            'pt': 'sobre-nos.html'
        },
        'services.html': {
            'nl': 'diensten.html',
            'pt': 'servicos.html'
        },
        'news.html': {
            'nl': 'nieuws.html'
        },
        'sobre-nos.html': {
            'nl': 'over-ons.html',
            'en': 'about-us.html'
        },
        'servicos.html': {
            'nl': 'diensten.html',
            'en': 'services.html'
        },
        'contacto.html': {
            'nl': 'contact.html',
            'en': 'contact.html'
        },
        'politica-de-privacidade.html': {
            'nl': 'privacy.html',
            'en': 'privacy.html'
        },
        'termos-de-uso.html': {
            'nl': 'terms-gebruik.html',
            'en': 'terms-of-use.html'
        },
        'termos-de-servico.html': {
            'nl': 'terms-diensten.html',
            'en': 'terms-of-service.html'
        }
    },

    // Detect the current language based on URL
    detectLanguage: function() {
        const path = window.location.pathname;
        
        // Handle path with or without /ict-tilburg/ prefix
        const checkPath = function(segment) {
            return path.includes(`/${segment}/`);
        };
        
        // Explicitly check for English first
        if (checkPath('en')) {
            return 'en';
        } 
        // Check for Portuguese second
        else if (checkPath('pt')) {
            return 'pt';
        } 
        // Check for Dutch directory
        else if (checkPath('nl')) {
            return 'nl';
        } 
        // Root or any other path always defaults to Dutch
        else {
            // Explicitly set Dutch as default language in localStorage
            localStorage.setItem('preferred_language', 'nl');
            return 'nl';
        }
    },

    // Switch to the correct page in the selected language
    switchLanguage: function(targetLang) {
        // Get current filename and language
        const currentPath = window.location.pathname;
        const currentFilename = currentPath.split('/').pop() || 'index.html';
        const currentLang = this.detectLanguage();
        
        // Always store the selected language preference
        // But for Dutch, we want to ensure it's set as the default
        if (targetLang === 'nl') {
            localStorage.setItem('preferred_language', 'nl');
        } else {
            localStorage.setItem('preferred_language', targetLang);
        }
        
        // If already on the target language, do nothing
        if (currentLang === targetLang) {
            return;
        }
        
        // Find the equivalent page in the target language
        let targetFilename = currentFilename;
        
        // Check if we need to map to a different filename
        if (this.pageMappings[currentFilename] && this.pageMappings[currentFilename][targetLang]) {
            targetFilename = this.pageMappings[currentFilename][targetLang];
        }
        
        // Construct the target URL
        let targetUrl = this.getBasePath(); // Get the base path (e.g., /ict-tilburg/ or '')
        
        // Always add language directory prefix
        targetUrl += targetLang + '/';
        
        // Add the target filename
        targetUrl += targetFilename;
        
        // Redirect to the target URL
        window.location.href = targetUrl;
    },

    // Initialize language selector buttons
    initLanguageSelector: function() {
        const currentLang = this.detectLanguage();
        const langSelector = document.querySelector('.language-selector');
        
        if (langSelector) {
            const langButtons = langSelector.querySelectorAll('a, button');
            
            langButtons.forEach(button => {
                const buttonLang = button.getAttribute('data-lang');
                
                // Remove active class from all buttons
                button.classList.remove('active');
                
                // Add active class to current language button
                if (buttonLang === currentLang) {
                    button.classList.add('active');
                }
                
                // Update href attribute if it exists to include the base path
                const href = button.getAttribute('data-href');
                if (href) {
                    const basePath = this.getBasePath();
                    button.setAttribute('href', basePath + href);
                }
                
                // Add click event listener
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.switchLanguage(buttonLang);
                });
            });
        }
    }
};

// Initialize the language handler when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    languageHandler.initLanguageSelector();
});
