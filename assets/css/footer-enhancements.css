/* Footer enhancements for standardized structure */

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255,255,255,0.1);
    margin-top: 40px;
}

.footer-legal {
    display: flex;
    gap: 20px;
}

.footer-legal a {
    color: #ddd;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s;
}

.footer-legal a:hover {
    color: var(--secondary, #43b380);
}

.copyright {
    font-size: 0.9rem;
    color: #aaa;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .footer-bottom {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .footer-legal {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
}

/* Language selector enhancements */
.lang-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    opacity: 0.7;
    transition: opacity 0.3s;
    padding: 5px 8px;
    border-radius: 3px;
}

.lang-btn.active {
    opacity: 1;
    font-weight: 600;
    background-color: rgba(255,255,255,0.1);
}

.lang-btn:hover {
    opacity: 1;
    background-color: rgba(255,255,255,0.1);
}

/* Logo link styling */
.logo-link {
    display: flex;
    align-items: center;
    gap: 15px;
    text-decoration: none;
    color: inherit;
}

.logo-link:hover {
    text-decoration: none;
}

/* Navigation enhancements */
.nav-links {
    display: flex;
    list-style: none;
    gap: 25px;
    margin: 0;
    padding: 0;
}

.nav-item {
    text-decoration: none;
    color: var(--dark, #343a40);
    font-weight: 600;
    font-size: 1.1rem;
    transition: color 0.3s;
    position: relative;
}

.nav-item:hover {
    color: var(--primary, #2c5e92);
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary, #2c5e92);
    transition: width 0.3s;
}

.nav-item:hover::after {
    width: 100%;
}

/* Mobile menu responsive */
@media (max-width: 992px) {
    .nav-links {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        padding: 20px;
        gap: 15px;
    }
    
    .nav-links.show {
        display: flex;
    }
    
    .mobile-menu-btn {
        display: block;
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--primary, #2c5e92);
    }
}

@media (min-width: 993px) {
    .mobile-menu-btn {
        display: none;
    }
}
