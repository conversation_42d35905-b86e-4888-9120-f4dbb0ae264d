/* Common styles for ICT Tilburg website */

:root {
    --primary: #2c5e92; /* Added from index.html inline styles */
    --secondary: #43b380;
    --accent: #ff6b35;
    --light: #f8f9fa;
    --dark: #343a40;
    --gray: #6c757d;
    --font-main: 'Open Sans', sans-serif;
    --font-heading: 'Roboto', sans-serif;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-main); /* Changed from Arial, sans-serif */
    color: var(--dark); /* Added from index.html inline styles */
    line-height: 1.6;
    background-color: #f0f4f8; /* Added from index.html inline styles */
}

/* Common layout components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header styles */
header {
    background-color: #ffffff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

/* Navigation styles */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Enhanced Navigation Styles */
.header-top {
    background-color: var(--primary);
    color: white;
    padding: 0.5rem 0;
    font-size: 0.9rem;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    gap: 1.5rem;
}

.contact-info a {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s;
}

.contact-info a:hover {
    opacity: 0.8;
}

.language-selector {
    display: flex;
    gap: 1rem;
}

.language-selector a {
    color: white;
    text-decoration: none;
    opacity: 0.7;
    transition: opacity 0.3s;
    padding: 0.2rem 0.5rem;
}

.language-selector a.active {
    opacity: 1;
    font-weight: 600;
    border-bottom: 2px solid var(--accent);
}

.main-header {
    background: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

.nav-item {
    color: var(--dark);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.nav-item:hover,
.nav-item.active {
    color: var(--primary);
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary);
    transition: width 0.3s ease;
}

.nav-item:hover::after,
.nav-item.active::after {
    width: 100%;
}

/* Mobile Navigation */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary);
}

@media (max-width: 1024px) {
    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        display: flex;
    }

    .mobile-menu-btn {
        display: block;
    }

    .contact-info {
        display: none;
    }

    .language-selector {
        width: 100%;
        justify-content: center;
    }
}

/* Footer styles */
footer {
    background-color: #333;
    color: #fff;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Common components */
.button {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s;
}

/* Blog specific styles */
.blog-hero {

/* Styles moved from index.html */
.container { width: 90%; max-width: 1200px; margin: 0 auto; }
header { background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); position: sticky; top: 0; z-index: 100; }
.header-top { background-color: var(--primary); color: white; padding: 8px 0; font-size: 0.9rem; }
.header-top .container { display: flex; justify-content: space-between; align-items: center; }
.contact-info { display: flex; gap: 20px; }
.contact-info a { color: white; text-decoration: none; }
.language-selector { display: flex; gap: 10px; }
.lang-btn { background: none; border: none; color: white; cursor: pointer; font-size: 0.9rem; opacity: 0.7; }
.lang-btn.active { opacity: 1; font-weight: 600; text-decoration: underline; }
.main-header { padding: 15px 0; }
.logo-area { display: flex; align-items: center; gap: 15px; }
.logo { width: 60px; height: 60px; background-color: var(--secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.8rem; }
.logo-text { font-family: var(--font-heading); font-weight: 700; font-size: 1.8rem; color: var(--primary); }
.logo-text span { color: var(--secondary); }
nav ul { display: flex; list-style: none; gap: 25px; }
nav a { text-decoration: none; color: var(--dark); font-weight: 600; font-size: 1.1rem; transition: color 0.3s; position: relative; }
nav a:hover { color: var(--primary); }
nav a::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 0; height: 2px; background-color: var(--primary); transition: width 0.3s; }
nav a:hover::after { width: 100%; }
.mobile-menu-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--primary); }
.hero { background: linear-gradient(rgba(44, 94, 146, 0.8), rgba(44, 94, 146, 0.9)), url('https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1500&q=80'); background-size: cover; background-position: center; color: white; padding: 80px 0; text-align: center; }
.hero h1 { font-size: 2.8rem; margin-bottom: 20px; font-weight: 700; max-width: 800px; margin-left: auto; margin-right: auto; }
.hero p { font-size: 1.4rem; margin-bottom: 30px; max-width: 700px; margin-left: auto; margin-right: auto; }
.btn { display: inline-block; background-color: var(--accent); color: white; padding: 12px 30px; border-radius: 50px; text-decoration: none; font-weight: 600; font-size: 1.1rem; transition: all 0.3s; border: 2px solid var(--accent); }
.btn:hover { background-color: transparent; transform: translateY(-3px); box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
.btn-secondary { background-color: transparent; border: 2px solid white; margin-left: 15px; }
.btn-secondary:hover { background-color: white; color: var(--primary); }
.section { padding: 80px 0; }
.section-title { text-align: center; margin-bottom: 50px; }
.section-title h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 15px; position: relative; display: inline-block; }
.section-title h2::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background-color: var(--secondary); }
.section-title p { color: var(--gray); max-width: 700px; margin: 20px auto 0; }
.services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; }
.service-card { background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.05); transition: transform 0.3s; }
.service-card:hover { transform: translateY(-10px); }
.service-icon { background-color: var(--primary); color: white; font-size: 2.5rem; height: 120px; display: flex; align-items: center; justify-content: center; }
.service-content { padding: 25px; }
.service-content h3 { font-size: 1.4rem; margin-bottom: 15px; color: var(--primary); }
.about { background-color: white; }
.about-content { display: grid; grid-template-columns: 1fr 1fr; gap: 50px; align-items: center; }
.about-text h2 { font-size: 2.2rem; color: var(--primary); margin-bottom: 20px; }
.about-text p { margin-bottom: 20px; }
.highlight { background-color: rgba(67, 179, 128, 0.1); padding: 20px; border-left: 4px solid var(--secondary); margin: 25px 0; }
.about-image { border-radius: 10px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
.about-image img { width: 100%; height: auto; display: block; }
.steps { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 50px; }
.step { text-align: center; padding: 30px 20px; background-color: white; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.05); }
.step-number { width: 50px; height: 50px; background-color: var(--primary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; font-weight: 700; margin: 0 auto 20px; }
.testimonials { background-color: #e9f5f0; }
.testimonial-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; }
.testimonial { background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.05); position: relative; }
.testimonial::before { content: '\201C'; font-size: 5rem; position: absolute; top: -20px; left: 10px; color: rgba(67, 179, 128, 0.2); font-family: Georgia, serif; }
.testimonial p { font-style: italic; margin-bottom: 20px; }
.client { display: flex; align-items: center; gap: 15px; }
.client-avatar { width: 60px; height: 60px; border-radius: 50%; background-color: var(--gray); overflow: hidden; }
.client-info h4 { font-weight: 600; margin-bottom: 5px; }
.client-info p { font-style: normal; color: var(--gray); font-size: 0.9rem; margin: 0; }
.contact { background-color: white; }
.contact-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 50px; }
.contact-info { display: flex; flex-direction: column; gap: 25px; }
.contact-item { display: flex; gap: 15px; }
.contact-icon { width: 50px; height: 50px; background-color: rgba(44, 94, 146, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: var(--primary); font-size: 1.3rem; flex-shrink: 0; }
.contact-form { background-color: #f8f9fa; padding: 30px; border-radius: 10px; }
.form-group { margin-bottom: 20px; }
.form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
.form-control { width: 100%; padding: 12px 15px; border: 1px solid #ddd; border-radius: 5px; font-family: var(--font-main); font-size: 1rem; }
textarea.form-control { min-height: 150px; resize: vertical; }
footer { background-color: var(--dark); color: white; padding: 60px 0 30px; }
.footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px; }
.footer-col h3 { font-size: 1.3rem; margin-bottom: 20px; position: relative; padding-bottom: 10px; }
.footer-col h3::after { content: ''; position: absolute; bottom: 0; left: 0; width: 50px; height: 2px; background-color: var(--secondary); }
.footer-col ul { list-style: none; }
.footer-col ul li { margin-bottom: 10px; }
.footer-col a { color: #ddd; text-decoration: none; transition: color 0.3s; }
.footer-col a:hover { color: var(--secondary); }
.social-links { display: flex; gap: 15px; margin-top: 20px; }
.social-links a { display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 50%; background-color: rgba(255,255,255,0.1); color: white; transition: all 0.3s; }
.social-links a:hover { background-color: var(--secondary); transform: translateY(-3px); }
.copyright { text-align: center; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #aaa; }
@media (max-width: 992px) {
    .about-content, .contact-grid { grid-template-columns: 1fr; }
    .about-image { order: -1; }
}
@media (max-width: 768px) {
    .header-top .container { flex-direction: column; gap: 10px; }
    .hero h1 { font-size: 2.2rem; }
    .hero p { font-size: 1.1rem; }
    .btn { display: block; width: 100%; margin-bottom: 15px; }
    .btn-secondary { margin-left: 0; }
    nav ul { display: none; position: absolute; top: 100%; left: 0; width: 100%; background-color: white; flex-direction: column; gap: 0; box-shadow: 0 5px 10px rgba(0,0,0,0.1); }
    nav ul.show { display: flex; }
    nav ul li { width: 100%; }
    nav ul li a { display: block; padding: 15px 20px; border-bottom: 1px solid #eee; }
    .mobile-menu-btn { display: block; }
}

/* Mission section */
.mission-section {
    margin: 40px 0;
    text-align: center;
}

.mission-section h2 {
    color: var(--primary);
    margin-bottom: 20px;
}

.mission-section p {
    max-width: 800px;
    margin: 0 auto 25px;
}

.mission-section .btn {
    display: inline-block;
    margin-top: 15px;
    background-color: var(--secondary);
    color: #fff;
    padding: 8px 30px;
    border-radius: 18px;
    font-weight: bold;
    text-decoration: none;
}

/* Page-specific styles */

/* Hero section */
.hero {
    background: linear-gradient(rgba(44, 94, 146, 0.85), rgba(44, 94, 146, 0.90)), url('../images/hero-bg.jpg') center/cover;
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

/* Services section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

/* News section */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.news-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Contact form */
.contact-form {
    max-width: 600px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

/* Downloads section */
.downloads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.download-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

/* Partners section */
.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.partner-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
}

/* Responsive design for blog */
@media (max-width: 1000px) {
    .blog-main { 
        grid-template-columns: 1fr; 
    }
}
